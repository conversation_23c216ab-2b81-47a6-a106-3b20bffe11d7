# GameServer

## 项目概述

GameServer是一个基于Windows平台的多人在线游戏服务器，采用C++开发。该服务器支持多种游戏玩法和事件系统，提供稳定的网络通信、用户管理、角色系统和事件处理机制。

## 系统架构

GameServer采用模块化架构设计，主要由以下几个核心组件组成：

### 核心系统
- **Socket通信系统**：处理客户端连接和数据包通信
- **协议系统**：实现客户端与服务器之间的通信协议
- **对象管理系统**：管理游戏中的各类对象（玩家、怪物等）
- **地图系统**：支持多地图和跨服地图传送
- **定时器系统**：驱动游戏逻辑和事件处理

### 游戏功能
- **角色系统**：玩家角色属性、状态和技能管理
- **物品系统**：物品属性、装备、背包管理
- **怪物系统**：怪物AI、属性和行为控制
- **技能系统**：角色技能效果和伤害计算
- **工会系统**：公会创建、管理和战争系统

### 事件系统
- **血色城堡(Blood Castle)**：限时副本事件
- **混沌城堡(Chaos Castle)**：PVP竞技场事件
- **恶魔广场(Devil Square)**：怪物挑战事件
- **幻影神殿(Illusion Temple)**：团队PVP事件
- **克劳尔(Crywolf)**：团队防守事件
- **皇家护卫(Imperial Guardian)**：多层副本事件
- **TvT事件**：团队对抗赛事
- **GvG事件**：公会对抗赛事
- **俄罗斯轮盘(Russian Roulette)**：随机淘汰赛
- **隐藏与寻找(Hide and Seek)**：躲猫猫游戏
- **追逐与捕获(Run and Catch)**：追捕游戏

## 技术特点

1. **网络架构**：
   - 支持TCP/IP和UDP协议
   - 高效的数据包处理机制
   - 连接池管理

2. **多服务器架构**：
   - 游戏服务器(GameServer)
   - 连接服务器(JoinServer)
   - 数据服务器(DataServer)
   - 地图服务器(MapServer)

3. **安全机制**：
   - 防黑客攻击系统
   - 数据包加密通信
   - 技能使用检查

4. **定时器系统**：
   - 怪物AI定时器
   - 事件处理定时器
   - 视野更新定时器

5. **对象管理**：
   - 高效的对象池管理
   - 视野范围系统
   - 对象状态同步

## 系统需求

- **操作系统**：Windows 10/11
- **开发环境**：Visual Studio 2019/2022
- **依赖库**：
  - Win32 API
  - Winsock 2.2

## 配置和运行

1. **编译项目**：
   - 使用Visual Studio打开GameServer.vcxproj项目文件
   - 选择Release配置并编译项目

2. **配置文件**：
   - 服务器参数配置：Data/GameServerInfo - Common.dat
   - 事件配置：Data/GameServerInfo - Event.dat
   - 其他配置文件位于Data目录下

3. **运行服务器**：
   - 确保所有配置文件已正确设置
   - 运行GameServer.exe启动服务器

## 目录结构

- **Data/**：配置文件和数据表
- **Event/**：各种游戏事件的配置文件
- **Hack/**：安全检查和防黑系统
- **lua/**：Lua脚本支持

## 开发注意事项

1. 确保所有协议包格式正确，保持与客户端一致
2. 添加新功能时遵循现有模块化设计
3. 使用条件编译宏处理不同版本的功能差异
4. 注意对象池管理和内存使用
5. 遵循现有协议命名和编号规则
6. 新增事件需在事件管理器中注册
7. 添加新怪物时需更新怪物基础数据表

## 安全机制

### 包验证系统
- **HackCheck**：检测异常数据包
- **HackPacketCheck**：验证数据包完整性
- **HackSkillCheck**：检测非法技能使用

### 加密系统
- 使用自定义加密算法保护网络通信
- 敏感数据加密存储
- 会话密钥定期更新

### 异常检测
- 角色数据异常监控
- 行为模式分析
- 自动封禁可疑账号

## 配置详解

### 服务器配置
- **ServerInfo**：服务器基本参数
- **Common.dat**：核心功能配置
- **Event.dat**：事件相关配置

### 游戏数据配置
- **物品配置**：物品属性和掉落设置
- **怪物配置**：怪物属性和AI行为
- **地图配置**：地图属性和刷怪点

### 事件配置
- 各事件单独配置文件
- 事件奖励和难度设置
- 触发条件和时间设置

## 性能优化

### 服务器资源管理
- 使用内存池减少内存分配开销
- 优化对象视野处理减少网络负载
- 多线程处理提高并发性能

### 网络优化
- 数据包优化减少带宽使用
- 批量处理减少网络交互次数
- 区域同步策略优化

### 数据库交互优化
- 异步数据库操作
- 数据缓存策略
- 批量更新减少I/O操作

## 扩展开发

### 添加新地图
1. 在Map.h中定义地图ID和属性
2. 配置地图刷怪点和NPC位置
3. 设置地图环境参数
4. 在MapServerManager中注册地图

### 添加新事件
1. 创建事件类继承基础事件接口
2. 实现事件初始化、主循环和结束处理
3. 在ServerInfo中注册事件配置加载
4. 在事件调度系统中添加事件调用

### 添加新物品
1. 在ItemManager中注册物品ID和基础属性
2. 配置物品掉落和获取途径
3. 实现物品特殊效果
4. 添加相关协议支持客户端显示

## 版本信息

当前系统支持多个游戏版本，通过条件编译宏控制：
- GAMESERVER_UPDATE：版本控制宏
- GAMESERVER_TYPE：服务器类型控制宏
- PROTECT_STATE：安全级别控制宏

## 主要模块说明

### Socket管理
- **SocketManager**：TCP连接管理
- **SocketManagerUdp**：UDP连接管理
- **Connection**：连接状态维护

### 协议处理
- **Protocol**：主要协议包处理
- **JSProtocol**：连接服务器协议
- **DSProtocol**：数据服务器协议
- **ESProtocol**：额外服务器协议

### 对象系统
- **ObjectManager**：游戏对象管理
- **User**：玩家对象处理
- **Monster**：怪物对象处理
- **Viewport**：视野范围管理

### 物品系统
- **ItemManager**：物品管理
- **ItemOption**：物品属性
- **SocketItemOption**：镶嵌物品系统

### 事件系统
- **EventManager**：事件调度
- **BloodCastle**：血色城堡
- **ChaosCastle**：混沌城堡
- **DevilSquare**：恶魔广场
- **CastleSiege**：攻城战
- **Duel**：决斗系统
- **CustomEvents**：自定义事件系统 