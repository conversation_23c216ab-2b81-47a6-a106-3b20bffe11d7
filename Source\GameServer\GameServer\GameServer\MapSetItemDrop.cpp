#include "stdafx.h"
#include "MapSetItemDrop.h"
#include "ItemOptionRate.h"
#include "ItemManager.h"
#include "Map.h"
#include "MemScript.h"
#include "Util.h"

CMapSetItemDrop gMapSetItemDrop;

CMapSetItemDrop::CMapSetItemDrop() : m_loaded(false) {}
CMapSetItemDrop::~CMapSetItemDrop() {}

void CMapSetItemDrop::Clear() {
    m_mapConfigs.clear();
    m_groups.clear();
    m_loaded = false;
}

bool CMapSetItemDrop::Load(const char* filePath) {
    Clear();
    if (!ParseFile(filePath)) {
        LogAdd(LOG_RED, "[MapSetItemDrop] Failed to parse file: %s", filePath);
        return false;
    }
    FinalizeGroups();
    m_loaded = true;
    LogAdd(LOG_BLACK, "[MapSetItemDrop] Loaded %zu map configs, %zu groups", m_mapConfigs.size(), m_groups.size());
    return true;
}

bool CMapSetItemDrop::IsMapEnabled(int mapIndex) const {
    if (!m_loaded) return false;
    auto it = m_mapConfigs.find(mapIndex);
    if (it == m_mapConfigs.end()) return false;
    return (it->second.useGroupNumber > 0);
}

bool CMapSetItemDrop::DropForMap(int mapIndex, LPOBJ lpMonster, LPOBJ lpLastHitPlayer, int x, int y) {
    if (!m_loaded) return false;
    if (!OBJECT_RANGE(lpMonster->Index) || !OBJECT_RANGE(lpLastHitPlayer->Index)) return false;

    auto itCfg = m_mapConfigs.find(mapIndex);
    if (itCfg == m_mapConfigs.end()) return false;
    const MAP_SET_DROP_CONFIG& cfg = itCfg->second;

    auto itGrp = m_groups.find(cfg.useGroupNumber);
    if (itGrp == m_groups.end() || itGrp->second.items.empty()) return false;

    // Ownership: 0 last hit, 1 max damage
    LPOBJ owner = lpLastHitPlayer;
    if (cfg.ownershipType == 1) {
        int maxIdx = gObjMonsterGetTopHitDamageUser(lpMonster);
        if (OBJECT_RANGE(maxIdx)) owner = &gObj[maxIdx];
    }
    if (!OBJECT_RANGE(owner->Index) || gObj[owner->Index].Type != OBJECT_USER) return false;

    // Individual rate handling
    if (cfg.individualRate > 0) {
        // One roll with group-level chance
        if ((GetLargeRand() % 1000000) >= cfg.individualRate) return false;
        if (cfg.dropAllSets == 1) {
            return DropAllInGroup(owner, itGrp->second, mapIndex, x, y, cfg);
        } else {
            return DropSingleFromGroup(owner, itGrp->second, mapIndex, x, y, cfg);
        }
    } else {
        // Per-item probability mode
        return RollPerItemInGroup(owner, itGrp->second, mapIndex, x, y, cfg);
    }
}

bool CMapSetItemDrop::DropAllInGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config) {
    if (group.items.empty()) return false;
    // Use the first item in group for base options when drop-all (except set value which uses each item value)
    const DROP_SET_ITEM_INFO* first = &group.items.front();
    bool any = false;
    int px = x, py = y;
    for (const auto& def : group.items) {
        if (gObjGetRandomFreeLocation(mapIndex, &px, &py, 3, 3, 10) == 0) { px = x; py = y; }
        any |= CreateAndDropItem(lpOwner, mapIndex, px, py, def, first);
    }
    return any;
}

bool CMapSetItemDrop::DropSingleFromGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config) {
    if (group.items.empty()) return false;
    // 组级单独爆率模式：应从组内“等概率”随机选一个物品，不使用每行的 probability 字段
    int idx = GetLargeRand() % (int)group.items.size();
    const DROP_SET_ITEM_INFO& picked = group.items[idx];
    return CreateAndDropItem(lpOwner, mapIndex, x, y, picked, &picked);
}

bool CMapSetItemDrop::RollPerItemInGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config) {
    bool any = false;
    int px = x, py = y;
    for (const auto& def : group.items) {
        if (def.probability <= 0) continue;
        if ((GetLargeRand() % 1000000) >= def.probability) continue;
        if (gObjGetRandomFreeLocation(mapIndex, &px, &py, 3, 3, 10) == 0) { px = x; py = y; }
        any |= CreateAndDropItem(lpOwner, mapIndex, px, py, def, &def);
    }
    return any;
}

bool CMapSetItemDrop::CreateAndDropItem(LPOBJ lpOwner, int map, int x, int y, const DROP_SET_ITEM_INFO& def, const DROP_SET_ITEM_INFO* firstOfGroup) {
    WORD itemIndex = MakeItemIndex(def.category1, def.category2);

    // Validate item
    ITEM_INFO info;
    if (gItemManager.GetInfo(itemIndex, &info) == 0 || info.DropItem == 0) {
        return false;
    }

    // When dropping ALL items of a group, base options (except ancient set option) should
    // use the FIRST line of the group. Ancient set option still follows each item definition.
    const DROP_SET_ITEM_INFO& base = (firstOfGroup != nullptr ? *firstOfGroup : def);
    // 是否使用固定卓越掩码（101~163 对应 1~63 位掩码），如果是则后续不再调用 MakeNewOption
    const bool fixedExcellentMask = (base.excellent >= 101 && base.excellent <= 163);

    BYTE level = CalcItemLevel(base);
    BYTE opt1 = 0, opt2 = 0, opt3 = 0, newOpt = 0, setOpt = 0;
    BYTE sock[MAX_SOCKET_OPTION] = {0xFF,0xFF,0xFF,0xFF,0xFF};

    // Calculate base options from 'base'
    CalcOptions(base, itemIndex, opt1, opt2, opt3, newOpt, setOpt, sock);

    // Override ancient set option with the item's own set value when different from base
    if (&base != &def) {
        BYTE tmpSet = 0;
        // Recalculate only the ancient set option using 'def'
        BYTE dummy1 = 0, dummy2 = 0, dummy3 = 0, dummyNew = newOpt; // keep previously rolled exc
        BYTE dummySock[MAX_SOCKET_OPTION] = {sock[0], sock[1], sock[2], sock[3], sock[4]};
        CalcOptions(def, itemIndex, dummy1, dummy2, dummy3, dummyNew, tmpSet, dummySock);
        setOpt = tmpSet;
    }

    // Post-process options according to server rules
    level   = ((itemIndex >= GET_ITEM(12, 0)) ? 0 : level);
    opt1    = ((itemIndex >= GET_ITEM(12, 0)) ? 0 : opt1);
    opt2    = ((itemIndex >= GET_ITEM(12, 0)) ? 0 : opt2);
    newOpt  = ((itemIndex >= GET_ITEM(12, 0)) ? 0 : newOpt);

    if (!fixedExcellentMask) {
        gItemOptionRate.MakeNewOption(itemIndex, newOpt, &newOpt);
    }
    gItemOptionRate.MakeSetOption(itemIndex, setOpt, &setOpt);
    gItemOptionRate.MakeSocketOption(itemIndex, sock[0], &sock[0]);

    GDCreateItemSend(lpOwner->Index, map, (BYTE)x, (BYTE)y, itemIndex, level, def.durability, opt1, opt2, opt3,
                     lpOwner->Index, newOpt, setOpt, 0, 0, sock, 0xFF, 0);
    return true;
}

BYTE CMapSetItemDrop::CalcItemLevel(const DROP_SET_ITEM_INFO& def) {
    BYTE level = 0;
    // 兼容“原系统等级”哨兵
    if (def.minLevel == 255 && def.maxLevel == 255) {
        gItemOptionRate.GetItemOption0(2, &level); // original system
    } else {
        // 配置限定 0..15，且最大等级必须 >= 最小等级
        int minL = std::max(0, std::min(15, def.minLevel));
        int maxL = std::max(0, std::min(15, def.maxLevel));
        if (maxL < minL) std::swap(maxL, minL);
        int range = (maxL - minL + 1);
        level = (BYTE)(minL + (GetLargeRand() % std::max(1, range)));
    }
    return level;
}

void CMapSetItemDrop::CalcOptions(const DROP_SET_ITEM_INFO& def, WORD itemIndex,
                                  BYTE& optSkill, BYTE& optLuck, BYTE& optAdd, BYTE& newOption,
                                  BYTE& setOption, BYTE socketOption[MAX_SOCKET_OPTION]) {
    // Skill
    if (def.skill == 100) { gItemOptionRate.GetItemOption1(2, &optSkill); }
    else if (def.skill > 0 && (GetLargeRand() % 100) < def.skill) { optSkill = 1; }
    else { optSkill = 0; }

    // Luck
    if (def.luck == 100) { gItemOptionRate.GetItemOption2(2, &optLuck); }
    else if (def.luck > 0 && (GetLargeRand() % 100) < def.luck) { optLuck = 1; }
    else { optLuck = 0; }

    // Addition
    if (def.addition == 3) {
        gItemOptionRate.GetItemOption3(2, &optAdd);
    } else if (def.addition >= 11 && def.addition <= 17) {
        // fixed +4..+28
        optAdd = (BYTE)(def.addition - 11 + 1);
    } else if (def.addition == 8) {
        optAdd = 7; // +28
    } else if (def.addition >= 1 && def.addition <= 7) {
        // 随机追 0..28，配置为 1..7 时表示随机 0..(配置) 档，需包含上界
        optAdd = (BYTE)(GetLargeRand() % (def.addition + 1));
    } else {
        optAdd = 0;
    }

    // Excellent
    if (def.excellent == 255) {
        // 原系统随机（走 Option4 组2）返回“数量”，随后由 MakeNewOption 生成掩码
        gItemOptionRate.GetItemOption4(2, &newOption);
    } else if (def.excellent >= 101 && def.excellent <= 163) {
        // 101~163 => 固定掩码(1~63)
        newOption = (BYTE)(def.excellent - 100);
    } else if (def.excellent >= 1 && def.excellent <= 6) {
        // 1~6 => 固定数量，随后由 MakeNewOption 把数量转成随机掩码
        newOption = (BYTE)def.excellent;
    } else if (def.excellent == 7) {
        // 7 => 全属性卓越：赋值为6个，MakeNewOption会受物品上限约束，尽可能给满
        newOption = 6;
    } else {
        newOption = 0;
    }

    // Ancient Set option
    if (def.setValue == 255) {
        gItemOptionRate.GetItemOption5(2, &setOption);
    } else if (def.setValue >= 5 && def.setValue <= 10) {
        setOption = (BYTE)def.setValue;
    } else if (def.setValue >= 1 && def.setValue <= 3) {
        gItemOptionRate.GetItemOption5(def.setValue, &setOption);
    } else {
        setOption = 0;
    }

    // Socket
    gItemOptionRate.GetItemOption6(IsTwoHanded(itemIndex) ? 4 : 3, &socketOption[0]);
}

void CMapSetItemDrop::FinalizeGroups() {
    for (auto& kv : m_groups) {
        int total = 0;
        for (auto& it : kv.second.items) {
            if (it.probability > 0) total += it.probability;
        }
        kv.second.totalWeight = total;
    }
}

bool CMapSetItemDrop::ParseFile(const char* filePath) {
    CMemScript* lpMemScript = new CMemScript;

    if (lpMemScript == 0)
    {
        ErrorMessageBox(MEM_SCRIPT_ALLOC_ERROR, filePath);
        return false;
    }

    if (lpMemScript->SetBuffer(filePath) == 0)
    {
        ErrorMessageBox(lpMemScript->GetLastError());
        delete lpMemScript;
        return false;
    }

    try
    {
        while (true)
        {
            if (lpMemScript->GetToken() == TOKEN_END)
            {
                break;
            }

            int section = lpMemScript->GetNumber();

            while (true)
            {
                if (section == 0)
                {
                    if (strcmp("end", lpMemScript->GetAsString()) == 0)
                    {
                        break;
                    }

                    MAP_SET_DROP_CONFIG cfg{};
                    cfg.mapIndex        = lpMemScript->GetNumber();
                    cfg.useGroupNumber  = lpMemScript->GetAsNumber();
                    cfg.dropAllSets     = lpMemScript->GetAsNumber();
                    cfg.ownershipType   = lpMemScript->GetAsNumber();
                    cfg.addGroup        = lpMemScript->GetAsNumber();
                    cfg.excCountGroup   = lpMemScript->GetAsNumber();
                    cfg.weaponExcGroup  = lpMemScript->GetAsNumber();
                    cfg.armorExcGroup   = lpMemScript->GetAsNumber();
                    cfg.individualRate  = lpMemScript->GetAsNumber();
                    cfg.noDropSetGroup  = lpMemScript->GetAsNumber();

                    m_mapConfigs[cfg.mapIndex] = cfg;
                }
                else if (section >= 1)
                {
                    if (strcmp("end", lpMemScript->GetAsString()) == 0)
                    {
                        break;
                    }

                    DROP_SET_ITEM_INFO di{};
                    int group = lpMemScript->GetNumber();
                    di.group       = group;
                    di.category1   = lpMemScript->GetAsNumber();
                    di.category2   = lpMemScript->GetAsNumber();
                    di.minLevel    = lpMemScript->GetAsNumber();
                    di.maxLevel    = lpMemScript->GetAsNumber();
                    di.skill       = lpMemScript->GetAsNumber();
                    di.luck        = lpMemScript->GetAsNumber();
                    di.addition    = lpMemScript->GetAsNumber();
                    di.durability  = lpMemScript->GetAsNumber();
                    di.excellent   = lpMemScript->GetAsNumber();
                    di.setValue    = lpMemScript->GetAsNumber();
                    di.probability = lpMemScript->GetAsNumber();

                    DROP_SET_GROUP_INFO& gi = m_groups[group];
                    gi.groupIndex = group;
                    gi.items.push_back(di);
                }
                else
                {
                    break;
                }
            }
        }
    }
    catch (...)
    {
        ErrorMessageBox(lpMemScript->GetLastError());
        delete lpMemScript;
        return false;
    }

    delete lpMemScript;
    return true;
}

bool CMapSetItemDrop::IsTwoHanded(WORD itemIndex) {
    return (gItemManager.GetItemTwoHand(itemIndex) != 0);
}

WORD CMapSetItemDrop::MakeItemIndex(int cat, int idx) {
    return GET_ITEM(cat, idx);
}


