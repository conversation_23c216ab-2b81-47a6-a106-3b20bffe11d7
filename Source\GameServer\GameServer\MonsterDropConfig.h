// MonsterDropConfig.h: interface for the CMonsterDropConfig class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "User.h"
#include <vector>
#include <map>
#include <string>

/**
 * @brief Monster drop base configuration structure
 * Contains basic monster drop settings
 */
struct MONSTER_DROP_BASE_INFO {
    int monsterLevel;           // Monster level
    int useFixedDrop;          // Use fixed drop (0/1)
    int fixedDropCount;        // Fixed drop count
    int dropLevel;             // Drop level
    int dropRange;             // Drop range
    int reserved1;             // Reserved field 1
    int reserved2;             // Reserved field 2
    int reserved3;             // Reserved field 3
    
    MONSTER_DROP_BASE_INFO() {
        monsterLevel = 0;
        useFixedDrop = 0;
        fixedDropCount = 0;
        dropLevel = 0;
        dropRange = 0;
        reserved1 = 0;
        reserved2 = 0;
        reserved3 = 0;
    }
};

/**
 * @brief Monster drop level group configuration
 * Contains drop level grouping settings
 */
struct MONSTER_DROP_LEVEL_GROUP {
    int useLevel;              // Use level (-1 for specific drop)
    int dropRate;              // Drop rate
    int additionalMethod;      // Additional method
    int excellentOption;       // Excellent option
    int ancientOption;         // Ancient option
    int setOption;             // Set option
    int socketOption;          // Socket option
    int minLevel;              // Minimum level
    int maxLevel;              // Maximum level
    int reserved1;             // Reserved field 1
    int reserved2;             // Reserved field 2
    
    MONSTER_DROP_LEVEL_GROUP() {
        useLevel = 0;
        dropRate = 0;
        additionalMethod = 0;
        excellentOption = 0;
        ancientOption = 0;
        setOption = 0;
        socketOption = 0;
        minLevel = 0;
        maxLevel = 0;
        reserved1 = 0;
        reserved2 = 0;
    }
};

/**
 * @brief Specific item drop configuration
 * Contains detailed item drop settings
 */
struct MONSTER_DROP_ITEM_INFO {
    int level;                 // Level (-1 for specific drop)
    int itemType1;             // Item type 1
    int itemType2;             // Item type 2
    int minLevel;              // Minimum level
    int maxLevel;              // Maximum level
    int skillRate;             // Skill rate (0-100)
    int luckRate;              // Luck rate (0-100)
    int addOption;             // Additional option (0-7)
    int quality;               // Quality (0-255)
    int excellentOption;       // Excellent option
    int setItemValue;          // Set item value
    int socketOption;          // Socket option
    int duration;              // Duration in minutes
    int dropCondition;         // Drop condition (VIP level restriction)
    int reserved;              // Reserved field
    int dropRate;              // Drop rate
    
    MONSTER_DROP_ITEM_INFO() {
        level = 0;
        itemType1 = 0;
        itemType2 = 0;
        minLevel = 0;
        maxLevel = 0;
        skillRate = 0;
        luckRate = 0;
        addOption = 0;
        quality = 0;
        excellentOption = 0;
        setItemValue = 0;
        socketOption = 0;
        duration = 0;
        dropCondition = 0;
        reserved = 0;
        dropRate = 0;
    }
};

/**
 * @brief Complete monster drop configuration
 * Contains all configuration data for a monster
 */
struct MONSTER_DROP_CONFIG {
    int monsterId;             // Monster ID
    std::string description;   // Monster description
    MONSTER_DROP_BASE_INFO baseInfo;
    std::vector<MONSTER_DROP_LEVEL_GROUP> levelGroups;
    std::vector<MONSTER_DROP_ITEM_INFO> dropItems;
    
    MONSTER_DROP_CONFIG() {
        monsterId = 0;
        description = "";
        levelGroups.clear();
        dropItems.clear();
    }
};

/**
 * @class CMonsterDropConfig
 * @brief Monster drop configuration manager
 * 
 * This class is responsible for loading, parsing, and managing monster drop 
 * configuration files, providing item drop functionality based on configurations.
 */
class CMonsterDropConfig {
public:
    CMonsterDropConfig();
    virtual ~CMonsterDropConfig();
    
    /**
     * @brief Load drop configuration file for specified monster
     * @param monsterId Monster ID
     * @param filePath Configuration file path
     * @return true=success, false=failure
     */
    bool LoadConfig(int monsterId, const char* filePath);
    
    /**
     * @brief Process monster drop
     * @param lpMonsterObj Monster object
     * @param lpTargetObj Target player object
     * @return true=has drop, false=no drop
     */
    bool ProcessMonsterDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj);
    
    /**
     * @brief Get monster configuration info
     * @param monsterId Monster ID
     * @return Configuration info pointer, returns nullptr if not found
     */
    MONSTER_DROP_CONFIG* GetConfig(int monsterId);
    
    /**
     * @brief Clear all configuration data
     */
    void Clear();
    
    /**
     * @brief Check if configuration is loaded for monster
     * @param monsterId Monster ID
     * @return true=loaded, false=not loaded
     */
    bool IsConfigLoaded(int monsterId);

private:
    /**
     * @brief Parse configuration file
     * @param filePath File path
     * @param config Output configuration object
     * @return true=success, false=failure
     */
    bool ParseConfigFile(const char* filePath, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Parse section 0 - Basic information
     * @param lpMemScript Memory script object
     * @param config Configuration object
     * @return true=success, false=failure
     */
    bool ParseSection0(class CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Parse section 1 - Base configuration
     * @param lpMemScript Memory script object
     * @param config Configuration object
     * @return true=success, false=failure
     */
    bool ParseSection1(class CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Parse section 2 - Level groups
     * @param lpMemScript Memory script object
     * @param config Configuration object
     * @return true=success, false=failure
     */
    bool ParseSection2(class CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Parse section 3 - Item configurations
     * @param lpMemScript Memory script object
     * @param config Configuration object
     * @return true=success, false=failure
     */
    bool ParseSection3(class CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Calculate drop rate
     * @param baseRate Base drop rate
     * @param lpTargetObj Target player
     * @param lpMonsterObj Monster object
     * @return Final drop rate
     */
    int CalculateDropRate(int baseRate, LPOBJ lpTargetObj, LPOBJ lpMonsterObj);
    
    /**
     * @brief Create dropped item
     * @param lpTargetObj Target player
     * @param itemInfo Item information
     * @return true=success, false=failure
     */
    bool CreateDropItem(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo);
    
    /**
     * @brief Check drop condition
     * @param lpTargetObj Target player
     * @param condition Drop condition
     * @return true=condition met, false=condition not met
     */
    bool CheckDropCondition(LPOBJ lpTargetObj, int condition);
    
    /**
     * @brief Check level requirement
     * @param lpTargetObj Target player
     * @param itemInfo Item information
     * @return true=requirement met, false=requirement not met
     */
    bool CheckLevelRequirement(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo);
    
    /**
     * @brief Process fixed drop mode
     * @param lpMonsterObj Monster object
     * @param lpTargetObj Target player object
     * @param config Configuration object
     * @return true=has drop, false=no drop
     */
    bool ProcessFixedDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj, const MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief Process random drop mode
     * @param lpMonsterObj Monster object
     * @param lpTargetObj Target player object
     * @param config Configuration object
     * @return true=has drop, false=no drop
     */
    bool ProcessRandomDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj, const MONSTER_DROP_CONFIG& config);

private:
    std::map<int, MONSTER_DROP_CONFIG> m_MonsterConfigs;    // Monster configuration map
    bool m_bInitialized;                                    // Initialization flag
};

extern CMonsterDropConfig gMonsterDropConfig;

