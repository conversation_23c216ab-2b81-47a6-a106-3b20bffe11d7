//////////////////////////////////////////////////////////////////////
// AppointItemDrop.cpp - S9 Precision Drop System for S6
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "AppointItemDrop.h"
#include "DSProtocol.h"
#include "ItemManager.h"
#include "ItemOptionRate.h"
#include "Log.h"
#include "MemScript.h"
#include "RandomManager.h"
#include "SocketItemOption.h"
#include "Util.h"
#include "Monster.h"

CAppointItemDrop g_AppointItemDrop;

CAppointItemDrop::CAppointItemDrop()
{
    m_bLoaded = false;
    m_MonsterToGroup.clear();
    m_DropGroups.clear();
}

CAppointItemDrop::~CAppointItemDrop()
{
    ClearAllData();
}

void CAppointItemDrop::ClearAllData()
{
    m_MonsterToGroup.clear();
    m_DropGroups.clear();
    m_bLoaded = false;
}

bool CAppointItemDrop::LoadAppointItemDropScript(const char* szFileName)
{
    ClearAllData();

    pugi::xml_document file;
    pugi::xml_parse_result result = file.load_file(szFileName);

    if (result.status != pugi::status_ok)
    {
        LogAdd(LOG_RED, "[AppointItemDrop] Error loading %s: %s", szFileName, result.description());
        return false;
    }

    return ParseXMLFile(szFileName);
}

bool CAppointItemDrop::ParseXMLFile(const char* szFileName)
{
    pugi::xml_document file;
    pugi::xml_parse_result result = file.load_file(szFileName);

    if (result.status != pugi::status_ok)
    {
        LogAdd(LOG_RED, "[AppointItemDrop] XML parse error: %s", result.description());
        return false;
    }

    pugi::xml_node main = file.child("AppointItemDrop");
    
    if (!main)
    {
        LogAdd(LOG_RED, "[AppointItemDrop] Root node 'AppointItemDropManager' not found");
        return false;
    }

    // Load monster to group mappings
	pugi::xml_node monsterList = main.child("MonsterList");
	for (pugi::xml_node monster = monsterList.child("Monster"); monster; monster = monster.next_sibling("Monster"))
	{
		int monsterID = monster.attribute("ID").as_int();
		int groupID = monster.attribute("DropGroup").as_int();
		if (monsterID > 0 && groupID >= 0)
		{
			m_MonsterToGroup[monsterID] = groupID;
		}
	}

	// Load items in this group
	pugi::xml_node itemDrop = main.child("ItemDrop");
	for (pugi::xml_node dropGroup = itemDrop.child("DropGroup"); dropGroup; dropGroup = dropGroup.next_sibling("DropGroup"))
	{
		APPOINT_DROP_GROUP group;
		group.nGroupID = dropGroup.attribute("ID").as_int();
		group.nDenominator = dropGroup.attribute("Denominator").as_int(10000);
		group.nTotalDropRate = 0;
		if (group.nGroupID < 0 || group.nDenominator <= 0)
		{
			LogAdd(LOG_RED, "[AppointItemDrop] Invalid group ID or denominator: Group=%d, Denominator=%d", group.nGroupID, group.nDenominator);
			continue;
		}

		for (pugi::xml_node item = dropGroup.child("Item"); item; item = item.next_sibling("Item"))
		{
			APPOINT_DROP_ITEM dropItem;
			dropItem.nCategory = item.attribute("Cat").as_int();
			dropItem.nIndex = item.attribute("Index").as_int();
			dropItem.nMinLevel = item.attribute("MinLevel").as_int();
			dropItem.nMaxLevel = item.attribute("MaxLevel").as_int();
			dropItem.nSkill = item.attribute("Skill").as_int();
			dropItem.nLuck = item.attribute("Luck").as_int();
			dropItem.nOption = item.attribute("Option").as_int();
			dropItem.nExcellent = item.attribute("Exc").as_int();
			dropItem.nDropRate = item.attribute("DropRate").as_int();
			WORD itemIndex = GET_ITEM(dropItem.nCategory, dropItem.nIndex);
			ITEM_INFO tmpInfo;
			if (gItemManager.GetInfo(itemIndex, &tmpInfo) == 0)
			{
				LogAdd(LOG_RED, "[AppointItemDrop] Invalid item: Cat=%d, Index=%d", dropItem.nCategory, dropItem.nIndex);
				continue;
			}
			group.items.push_back(dropItem);
			group.nTotalDropRate += dropItem.nDropRate;
		}
		if (!group.items.empty())
		{
			group.CalculateCumulativeWeights();
			m_DropGroups[group.nGroupID] = std::move(group);
		}
	}

    m_bLoaded = true;
    LogAdd(LOG_GREEN, "[AppointItemDrop] Loaded %d monster mappings and %d drop groups", 
        m_MonsterToGroup.size(), m_DropGroups.size());

    return true;
}

bool CAppointItemDrop::AppointItemDrop(LPOBJ lpTargetObj, LPOBJ lpMonsterObj)
{
    // Fast validation - early return for invalid parameters
    if (!m_bLoaded || !OBJECT_RANGE(lpTargetObj->Index) || !OBJECT_RANGE(lpMonsterObj->Index))
    {
        return false;
    }

	// Ensure target is the player with max hit damage on this monster
	int maxHitUserIndex = gObjMonsterGetTopHitDamageUser(lpMonsterObj);
	if (OBJECT_RANGE(maxHitUserIndex))
	{
		lpTargetObj = &gObj[maxHitUserIndex];
	}

    // Fast lookup using unordered_map (O(1) average case vs O(log n) for map)
    const auto monsterIt = m_MonsterToGroup.find(lpMonsterObj->Class);
    if (monsterIt == m_MonsterToGroup.cend())
    {
        return false; // This monster has no appointed drops
    }

    // Fast lookup for drop group
    const auto groupIt = m_DropGroups.find(monsterIt->second);
    if (groupIt == m_DropGroups.cend())
    {
        LogAdd(LOG_RED, "[AppointItemDrop] Drop group %d not found for monster %d", 
            monsterIt->second, lpMonsterObj->Class);
        return false;
    }

    const APPOINT_DROP_GROUP& group = groupIt->second;
    
    // Quick validation
    if (group.nTotalDropRate <= 0)
    {
        return false;
    }

    // Roll for drop occurrence
    int dropChance = GetLargeRand() % group.nDenominator;
    if (dropChance >= group.nTotalDropRate)
    {
        return false; // No drop
    }

    // Use optimized binary search item selection (O(log n) vs O(n))
    int selectedItem = group.GetRandomItemIndex();
    if (selectedItem < 0 || selectedItem >= static_cast<int>(group.items.size()))
    {
        return false;
    }

    // Create the item
    const bool result = CreateItem(lpTargetObj, group.items[selectedItem]);
    
    // Reduced logging - only log successful drops or errors
    if (result)
    {
        LogDropResult(lpMonsterObj->Class, group.items[selectedItem].nCategory, 
            group.items[selectedItem].nIndex, result);
    }

    return result;
}

int CAppointItemDrop::CalculateDropItem(const APPOINT_DROP_GROUP& group)
{
    // This function is now deprecated in favor of the optimized GetRandomItemIndex()
    // Kept for compatibility, but delegates to the optimized version
    return group.GetRandomItemIndex();
}

bool CAppointItemDrop::CreateItem(LPOBJ lpTargetObj, const APPOINT_DROP_ITEM& item)
{
    WORD itemIndex = GET_ITEM(item.nCategory, item.nIndex);
    
    if (gItemManager.CheckItemMake(itemIndex) == 0)
    {
        return false;
    }

    // ✅ Calculate item level with safety checks
    BYTE itemLevel = 0;
    if (item.nMaxLevel > item.nMinLevel)
    {
        itemLevel = item.nMinLevel + (GetLargeRand() % (item.nMaxLevel - item.nMinLevel + 1));
    }
    else
    {
        itemLevel = item.nMinLevel;
    }
    // Ensure level is within valid range (0-15)
    itemLevel = (itemLevel > 15) ? 15 : itemLevel;

    // ✅ Set options based on configuration parameters with probability
	BYTE ItemLevel = itemLevel;  // Use calculated level
	BYTE ItemOption1 = 0;  // Skill
	BYTE ItemOption2 = 0;  // Luck  
	BYTE ItemOption3 = 0;  // Addition
	BYTE ItemNewOption = 0;  // Excellent
	BYTE ItemSetOption = 0;
	BYTE ItemSocketOption[MAX_SOCKET_OPTION] = { 0xFF,0xFF,0xFF,0xFF,0xFF };

	// ✅ Skill: 概率性生成 (配置值 × 20% 概率)
	if (item.nSkill > 0) {
		int skillChance = item.nSkill * 20;  // Skill="1" → 20%概率
		if (skillChance > 100) skillChance = 100;
		if ((GetLargeRand() % 100) < skillChance) {
			ItemOption1 = 1;
		}
	}

	// ✅ Luck: 概率性生成 (配置值 × 15% 概率)
	if (item.nLuck > 0) {
		int luckChance = item.nLuck * 15;   // Luck="1" → 15%概率
		if (luckChance > 100) luckChance = 100;
		if ((GetLargeRand() % 100) < luckChance) {
			ItemOption2 = 1;
		}
	}

	// ✅ Addition: 概率性生成 (配置值 × 12% 概率)
	if (item.nOption > 0) {
		int additionChance = item.nOption * 12;  // Option="3" → 36%概率
		if (additionChance > 100) additionChance = 100;
		if ((GetLargeRand() % 100) < additionChance) {
			// 随机生成1到配置值之间的追加等级
			ItemOption3 = 1 + (GetLargeRand() % item.nOption);
			if (ItemOption3 > 7) ItemOption3 = 7;  // 限制最大+28
		}
	}

	// ✅ Excellent: 概率性生成 (配置值 × 8% 概率) 
	if (item.nExcellent > 0) {
		int excellentChance = item.nExcellent * 8;  // Exc="5" → 40%概率
		if (excellentChance > 100) excellentChance = 100;
		if ((GetLargeRand() % 100) < excellentChance) {
			// 随机生成1到配置值之间的卓越数量
			ItemNewOption = 1 + (GetLargeRand() % item.nExcellent);
			if (ItemNewOption > 6) ItemNewOption = 6;  // 限制最大6个
		}
	}

	// ✅ Generate excellent option mask from configured count
	if (ItemNewOption > 0)
	{
		gItemOptionRate.MakeNewOption(itemIndex, ItemNewOption, &ItemNewOption);
	}

	// ✅ Generate set option (use original system for now)
	gItemOptionRate.GetItemOption5(2, &ItemSetOption);
	gItemOptionRate.MakeSetOption(itemIndex, ItemSetOption, &ItemSetOption);

	// ✅ Generate socket option (use original system for now)
	gItemOptionRate.GetItemOption6(2, &ItemSocketOption[0]);
	gItemOptionRate.MakeSocketOption(itemIndex, ItemSocketOption[0], &ItemSocketOption[0]);

    // Create the item
	GDCreateItemSend(lpTargetObj->Index, lpTargetObj->Map, (BYTE)lpTargetObj->X, (BYTE)lpTargetObj->Y, itemIndex, ItemLevel, 0, ItemOption1, ItemOption2, ItemOption3, lpTargetObj->Index, ItemNewOption, ItemSetOption, 0, 0, ItemSocketOption, 0xFF, 0);

    return true;
}

void CAppointItemDrop::LogDropResult(int nMonsterClass, int nItemCat, int nItemIndex, bool bSuccess)
{
    if (bSuccess)
    {
        LogAdd(LOG_BLACK, "[AppointItemDrop] Monster %d dropped item (%d,%d)", 
            nMonsterClass, nItemCat, nItemIndex);
    }
}
