#pragma once
#define EQUIPMENT_NEW_LENGTH				13//25
#define MAX_CHARACTER_LENGTH				10
#define CHAOS_MACHINE_EXTENSION

#define INVENTORY_HOLY_JOH
#define TRADE_SYSTEM_COIN
#define INVENTORY_EXT_BUY
#define INVASION_TIME_MANAGER_KILL
#define EVENT_FIND_PATH_NPC_CLIK
#define CHARACTER_EQUIPEMENT_EXT
#define MOD_PET_EXPERIENCE
#define SHUTDOWN_CORE_WING4
#define PARTY_PASSWORD_JOIN
#define LIMIT_ACTION_KILL_MONSTER			1
#define COLOR_ITEM_DEF						0
#define ONLINE_VIP_USER_SWITCH
#define GS_CUSTOM_DAT_MSG_ID
#define temporarily_deactivate				1


#define INDEX_SET(index, data) \
	index[0] = SET_NUMBERHB(data); \
	index[1] = SET_NUMBERLB(data);

#define INDEX_GET(index) \
	MAKE_NUMBERW(index[0], index[1]);

#define INDEX_DATA(name) \
	BYTE m_prop##name[2]; \
	WORD Get##name() { return INDEX_GET(this->m_prop##name); } \
	void Set##name(WORD value) { INDEX_SET(this->m_prop##name, value); }


struct UInt16
{
	uint8_t data[2]; // [0] = high, [1] = low

	UInt16() { data[0] = data[1] = 0; }
	UInt16(uint16_t v) { set(v); }

	void set(uint16_t v) {
		data[0] = static_cast<uint8_t>((v >> 8) & 0xFF); // High
		data[1] = static_cast<uint8_t>(v & 0xFF);        // Low
	}

	uint16_t get() const {
		return static_cast<uint16_t>((data[0] << 8) | data[1]);
	}

	// operadores para que se use casi igual que un uint16_t
	UInt16& operator=(uint16_t v) { set(v); return *this; }
	operator uint16_t() const { return get(); }
};

