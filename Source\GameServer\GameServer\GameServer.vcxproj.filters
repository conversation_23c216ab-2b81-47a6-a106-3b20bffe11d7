﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\Events">
      <UniqueIdentifier>{d800876a-794c-4ae4-a011-4196afed9fbc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Events">
      <UniqueIdentifier>{ed0a5c61-1fb3-4f1b-8154-466eec6886a8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Drop">
      <UniqueIdentifier>{72981b54-c660-49c3-b428-a846cb39417f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="small.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="GameServer.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\Util\icone.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\util\bitmap2.bmp">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GameServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ThemidaSDK.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SocketManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalSection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IpManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PacketManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ServerDisplayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillHitBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemScript.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Message.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SerialCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Trade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ResetTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ServerInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DefaultClassInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Duel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ExperienceTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Fruit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GameMaster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Gate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MapItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MapPath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LuckyCoin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Map.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ChaosCastle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ComboSkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Effect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EffectManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Item.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemBag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemBagManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemDrop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemStack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JewelMix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JewelOfHarmonyOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JewelOfHarmonyType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JSProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Monster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIAgro.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIAutomata.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIAutomataInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIElement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIElementInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIGroup.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIGroupMember.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIMovePath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIMovePathInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIRuleInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIUnit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIUnitInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterAIUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSetBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillElement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillElementInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillElementOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillUnit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterSkillUnitInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MossMerchant.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Move.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MoveSummon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Notice.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NpcTalk.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="380ItemOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="380ItemType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Attack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CannonTower.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CashShop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="User.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Viewport.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Warehouse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CommandManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ESProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HackCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HackPacketCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Helper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MapServerManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MasterSkillTree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Mercenary.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Party.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Path.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PersonalShop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Protect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Protocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Quest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QuestObjective.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QuestReward.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QuestWorld.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QuestWorldObjective.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QuestWorldReward.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SetItemOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SetItemType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Shop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShopManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Skill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SocketItemOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SocketItemType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GameMain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DSProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ObjectManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Union.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UnionInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ChaosBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GensSystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GuardianStatue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Connection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DarkSpirit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Guild.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GuildClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SocketManagerUdp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MiniDump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MapManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomWing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomJewel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemMove.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemoryAllocator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemoryAllocatorInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MiniMap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PentagramSystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RandomManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MiningSystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Reconnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LuckyItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemOptionRate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SummonScroll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ScheduleManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemBagEx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomMonster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomStore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MuunSystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomArena.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GuildMatching.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PartyMatching.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemValue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PcPoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CSProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QueueTimer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemOption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MuRummy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomAttack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomMove.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BotBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NewsProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomOnlineLottery.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReadScript2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Command.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomQuest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomTop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomRankUser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomWingMix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomNpcCollector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomDeathMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomPick.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomRanking.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomBuyVip.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomCombo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomCommandDescription.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomEventTime.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FilterRename.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArcaBattle.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="BattleGround.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="BattleSoccer.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="BloodCastle.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="BattleSoccerManager.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="BonusManager.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleDeep.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleSiege.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleSiegeCrown.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleSiegeCrownSwitch.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleSiegeSync.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CastleSiegeWeapon.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="Crywolf.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfAltar.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfObjInfo.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfStateTimeInfo.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfStatue.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfSync.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CrywolfUtil.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="DevilSquare.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="DoubleGoer.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventHideAndSeek.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventInventory.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventKillAll.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventPvp.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventQuickly.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventRunAndCatch.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventRussianRoulette.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventStart.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="IllusionTemple.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="ImperialGuardian.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="InvasionManager.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="InventoryEquipment.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="Kalima.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="Kanturu.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuBattleOfMaya.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuBattleOfNightmare.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuBattleStanby.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuBattleUserMng.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuEntranceNPC.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuMaya.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuMonsterMng.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuObjInfo.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuStateInfo.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuTowerOfRefinement.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="KanturuUtil.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="LifeStone.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="Raklion.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionBattleOfSelupan.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionBattleUser.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionBattleUserMng.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionObjInfo.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionSelupan.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="RaklionUtil.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CGMBattleIce3.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="EventTvT.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CustomQuiz.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CustomEventDrop.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CustomNpcMove.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HackSkillCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomNpcCommand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemValueTrade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomNpcQuest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomStartItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EventGvG.h">
      <Filter>Header Files\Events</Filter>
    </ClInclude>
    <ClInclude Include="CustomMix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomExchangeCoin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillDamage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Util\CCRC32.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pugiconfig.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pugixml.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CEventName.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CGMMixExpansion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CharacterAdvance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GMHolyItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ChatManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EventFindPath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CGMPetManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CGMFlagNatManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CGMEarringManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MultiLanguage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WindowsConsole.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ConsoleDebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MyTimer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MasterResetTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CGMHardwareId.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="define_global.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomItemUseVip.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemColorMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AppointItemDrop.h">
      <Filter>Drop</Filter>
    </ClInclude>
    <ClInclude Include="MapSetItemDrop.h">
      <Filter>Drop</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GameServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SocketManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CriticalSection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IpManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PacketManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ServerDisplayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SkillHitBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MemScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Message.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SerialCheck.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Trade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ServerInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ResetTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DefaultClassInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Duel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ExperienceTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Filter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Fruit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Gate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GameMaster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MapItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MapPath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="LuckyCoin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Map.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ComboSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Effect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EffectManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Item.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemBag.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemBagManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemDrop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemStack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="JewelMix.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="JewelOfHarmonyOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="JewelOfHarmonyType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="JSProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Kalima.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Monster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIAgro.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIAutomata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIAutomataInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIElement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIElementInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIGroup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIGroupMember.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIMovePath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIMovePathInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIRuleInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIUnit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIUnitInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterAIUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSetBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillElement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillElementInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillElementOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillUnit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MonsterSkillUnitInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MossMerchant.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Move.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MoveSummon.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Notice.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NpcTalk.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="380ItemOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="380ItemType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Attack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CannonTower.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CashShop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfSync.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="User.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Viewport.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Warehouse.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CommandManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ESProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HackCheck.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HackPacketCheck.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Helper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Party.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Path.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PersonalShop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Protect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Protocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Quest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QuestObjective.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QuestReward.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QuestWorld.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QuestWorldObjective.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QuestWorldReward.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SetItemOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SetItemType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Shop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ShopManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Skill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SkillManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SocketItemOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SocketItemType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GameMain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DSProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ObjectManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Union.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UnionInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ChaosBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GensSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GuardianStatue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Connection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MapServerManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DarkSpirit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Guild.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GuildClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MasterSkillTree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SocketManagerUdp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MiniDump.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MapManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomWing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomJewel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemMove.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="InventoryEquipment.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MemoryAllocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MemoryAllocatorInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MiniMap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PentagramSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RandomManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MiningSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Queue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Reconnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="LuckyItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemOptionRate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SummonScroll.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ScheduleManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemBagEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomMonster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomStore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MuunSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomArena.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GuildMatching.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PartyMatching.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemValue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PcPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CSProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QueueTimer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemOption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MuRummy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomAttack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomMove.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BotBuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomOnlineLottery.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Command.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomQuest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomTop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomRankUser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomWingMix.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomNpcCollector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomDeathMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomPick.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomRanking.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomBuyVip.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomCombo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomCommandDescription.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomEventTime.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FilterRaname.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ArcaBattle.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="BattleGround.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="BattleSoccer.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="BattleSoccerManager.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="BloodCastle.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="BonusManager.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleDeep.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleSiege.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleSiegeCrown.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleSiegeCrownSwitch.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleSiegeSync.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CastleSiegeWeapon.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="ChaosCastle.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="Crywolf.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfAltar.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfObjInfo.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfStateTimeInfo.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CrywolfStatue.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="DevilSquare.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="DoubleGoer.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventHideAndSeek.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventInventory.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventKillAll.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventPvP.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventQuickly.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventRunAndCatch.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventRussianRoulette.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventStart.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="IllusionTemple.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="ImperialGuardian.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="InvasionManager.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="Kanturu.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuBattleOfMaya.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuBattleOfNightmare.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuBattleStanby.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuBattleUserMng.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuEntranceNPC.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuMaya.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuMonsterMng.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuObjInfo.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuStateInfo.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuTowerOfRefinement.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="KanturuUtil.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="LifeStone.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="Mercenary.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="Raklion.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionBattleOfSelupan.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionBattleUser.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionBattleUserMng.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionObjInfo.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionSelupan.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="RaklionUtil.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CGMBattleIce3.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="EventTvT.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CustomEventDrop.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CustomQuiz.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CustomNpcMove.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HackSkillCheck.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomNpcCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemValueTrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomNpcQuest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomStartItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EventGvG.cpp">
      <Filter>Source Files\Events</Filter>
    </ClCompile>
    <ClCompile Include="CustomMix.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomExchangeCoin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SkillDamage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Util\CCRC32.Cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Util\Math.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pugixml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CEventName.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CGMMixExpansion.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CharacterAdvance.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GMHolyItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ChatManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EventFindPath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CGMPetManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CGMFlagNatManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CGMEarringManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MultiLanguage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WindowsConsole.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ConsoleDebug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MyTimer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MasterResetTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CGMHardwareId.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomItemUseVip.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ItemColorMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AppointItemDrop.cpp">
      <Filter>Drop</Filter>
    </ClCompile>
    <ClCompile Include="MapSetItemDrop.cpp">
      <Filter>Drop</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="GameServer.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>