//////////////////////////////////////////////////////////////////////
// AppointItemDrop.h - S9 Precision Drop System for S6
// Date: 2024
// Description: Precise item drop system for specific monsters
//////////////////////////////////////////////////////////////////////

#ifndef APPOINTITEMDROP_H
#define APPOINTITEMDROP_H

#pragma once

#include "User.h"
#include "pugixml.hpp"
#include <vector>
#include <map>
#include <unordered_map>
#include <algorithm>
#include "Util.h"

// Drop item structure
struct APPOINT_DROP_ITEM
{
    APPOINT_DROP_ITEM()
    {
        memset(this, 0, sizeof(APPOINT_DROP_ITEM));
    }

    int nCategory;          // Item category
    int nIndex;             // Item index
    int nMinLevel;          // Minimum level
    int nMaxLevel;          // Maximum level
    int nSkill;             // Skill attribute
    int nLuck;              // Luck attribute
    int nOption;            // Additional attribute
    int nExcellent;         // Excellent attribute
    int nDropRate;          // Drop weight
};

// Drop group structure with performance optimizations
struct APPOINT_DROP_GROUP
{
    APPOINT_DROP_GROUP()
    {
        nGroupID = 0;
        nDenominator = 10000;
        nTotalDropRate = 0;
        bWeightsCalculated = false;
    }

    int nGroupID;                              // Group ID
    int nDenominator;                          // Denominator for probability
    int nTotalDropRate;                        // Total drop rate
    std::vector<APPOINT_DROP_ITEM> items;      // Drop items list
    
    // Performance optimization: pre-calculated cumulative weights
    mutable std::vector<int> cumulativeWeights; // Cumulative weights for binary search
    mutable bool bWeightsCalculated;           // Flag to check if weights are calculated
    
    // Calculate cumulative weights for fast binary search
    void CalculateCumulativeWeights() const
    {
        if (bWeightsCalculated) return;
        
        cumulativeWeights.clear();
        cumulativeWeights.reserve(items.size());
        
        int cumulative = 0;
        for (const auto& item : items)
        {
            cumulative += item.nDropRate;
            cumulativeWeights.push_back(cumulative);
        }
        
        bWeightsCalculated = true;
    }
    
    // Get item using optimized binary search
    int GetRandomItemIndex() const
    {
        if (items.empty() || nTotalDropRate <= 0)
            return -1;
            
        CalculateCumulativeWeights();
        
        int randomValue = GetLargeRand() % nTotalDropRate;
        
        // Binary search for O(log n) performance instead of O(n)
        auto it = std::upper_bound(cumulativeWeights.begin(), 
                                   cumulativeWeights.end(), 
                                   randomValue);
        
        if (it != cumulativeWeights.end())
        {
            return static_cast<int>(std::distance(cumulativeWeights.begin(), it));
        }
        
        return -1;
    }
};

// Monster to group mapping
struct MONSTER_GROUP_LINK
{
    MONSTER_GROUP_LINK()
    {
        nMonsterIndex = 0;
        nDropGroupID = 0;
    }

    int nMonsterIndex;      // Monster class ID
    int nDropGroupID;       // Drop group ID
};

class CAppointItemDrop
{
public:
    CAppointItemDrop();
    ~CAppointItemDrop();

    // Core functions
    bool LoadAppointItemDropScript(const char* szFileName = "Data\\Drop\\IGC_AppointItemDrop.xml");
    bool AppointItemDrop(LPOBJ lpTargetObj, LPOBJ lpMonsterObj);
    void ClearAllData();
    
    // Status queries
    bool IsLoaded() const { return m_bLoaded; }
    int GetGroupCount() const { return (int)m_DropGroups.size(); }
    
private:
    bool m_bLoaded;
    std::unordered_map<int, int> m_MonsterToGroup;          // Monster ID -> Drop Group ID mapping (optimized)
    std::unordered_map<int, APPOINT_DROP_GROUP> m_DropGroups; // Drop group data (optimized)
    
    // Internal functions
    bool ParseXMLFile(const char* szFileName);
    bool CreateItem(LPOBJ lpTargetObj, const APPOINT_DROP_ITEM& item);
    int CalculateDropItem(const APPOINT_DROP_GROUP& group);
    void LogDropResult(int nMonsterClass, int nItemCat, int nItemIndex, bool bSuccess);
};

extern CAppointItemDrop g_AppointItemDrop;

#endif // APPOINTITEMDROP_H
