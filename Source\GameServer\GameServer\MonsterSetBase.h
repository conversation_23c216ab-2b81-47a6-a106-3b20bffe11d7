// MonsterSetBase.h: interface for the CMonsterSetBase class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "User.h"
#include "Map.h"
#define MAX_MSB_MONSTER			10000
#define OBJ_MAXMONSTER			(OBJECT_START_USER_AND_BOTS)

struct MONSTER_SET_BASE_INFO
{
	int index;
	int Type;
	int MonsterClass;
	uint16_t Map;
	uint8_t Dis;
	uint8_t Dir;
	uint8_t X;
	uint8_t Y;
	uint8_t TX;
	uint8_t TY;
	int Value;
	int Count;


	bool GetPosition(short* ox, short* oy);
	bool GetBoxPosition(int x, int y, int tx, int ty, short* ox, short* oy);
};

struct MONSTER_SET_REFLINK
{
	int index;
	int map;
	MONSTER_SET_REFLINK()
		: index(-1), map(-1) {}

	MONSTER_SET_REFLINK(int i, int m)
		: index (i), map(m) {}
};

class CMonsterSetBase
{
public:
	CMonsterSetBase();
	virtual ~CMonsterSetBase();
	void Load(std::string path, int _map = -1);
	void LoadFiles();
#if GAMESERVER_CLIENTE_UPDATE >= 16
	void ProcessFilesInDirectory(const std::string& directory);
#endif //GAMESERVER_CLIENTE_UPDATE >= 16
	void ClearAll();


	void SetInfo(int iMap, MONSTER_SET_BASE_INFO info);
	bool GetPosition(int index,short map,short* ox,short* oy);
	bool GetBoxPosition(int map,int x,int y,int tx,int ty,short* ox,short* oy);
	MONSTER_SET_BASE_INFO* FindMonsterByIndex(int _index);
	MONSTER_SET_BASE_INFO* FindMonsterByMap(int _map, int _index);
	std::vector<MONSTER_SET_BASE_INFO>& FindDataMonsterByMap(int _map);
private:
	int m_count;
	MONSTER_SET_REFLINK m_MonsterIndexTable[MAX_MSB_MONSTER];
	std::vector<MONSTER_SET_BASE_INFO> m_MonsterSetBaseInfo[MAX_MAP];
};

extern CMonsterSetBase gMonsterSetBase;