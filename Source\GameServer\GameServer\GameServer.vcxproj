﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_EX603CS|Win32">
      <Configuration>Release_EX603CS</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_EX603|Win32">
      <Configuration>Release_EX603</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{93F80160-07A8-42A6-B46C-70B72AFF74C4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>GameServer</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603CS|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603CS|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IntDir>Release\GameServer_EX603\</IntDir>
    <OutDir>..\..\..\..\MuServer\4.- GameServer</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603CS|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)Release\</OutDir>
    <IntDir>Release\GameServer_EX603CS\</IntDir>
    <TargetName>$(ProjectName)CS</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <GenerateMapFile>false</GenerateMapFile>
      <MapFileName>
      </MapFileName>
      <MapExports>false</MapExports>
      <AssemblyDebug>
      </AssemblyDebug>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;GAMESERVER_TYPE=0;GAMESERVER_UPDATE=603;GAMESERVER_LANGUAGE=1;GAMESERVER_LICENSE=0;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_EX603CS|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;GAMESERVER_TYPE=1;GAMESERVER_UPDATE=603;GAMESERVER_LANGUAGE=1;GAMESERVER_LICENSE=0;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\..\util\bitmap2.bmp" />
    <None Include="..\..\..\Util\icone.ico" />
    <None Include="GameServer.ico" />
    <None Include="ReadMe.txt" />
    <None Include="small.ico" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\Util\CCRC32.H" />
    <ClInclude Include="380ItemOption.h" />
    <ClInclude Include="380ItemType.h" />
    <ClInclude Include="AppointItemDrop.h" />
    <ClInclude Include="ArcaBattle.h" />
    <ClInclude Include="Attack.h" />
    <ClInclude Include="BattleGround.h" />
    <ClInclude Include="BattleSoccer.h" />
    <ClInclude Include="BattleSoccerManager.h" />
    <ClInclude Include="BloodCastle.h" />
    <ClInclude Include="BonusManager.h" />
    <ClInclude Include="BotBuffer.h" />
    <ClInclude Include="CannonTower.h" />
    <ClInclude Include="CashShop.h" />
    <ClInclude Include="CastleDeep.h" />
    <ClInclude Include="CastleSiege.h" />
    <ClInclude Include="CastleSiegeCrown.h" />
    <ClInclude Include="CastleSiegeCrownSwitch.h" />
    <ClInclude Include="CastleSiegeSync.h" />
    <ClInclude Include="CastleSiegeWeapon.h" />
    <ClInclude Include="CEventName.h" />
    <ClInclude Include="CGMEarringManager.h" />
    <ClInclude Include="CGMFlagNatManager.h" />
    <ClInclude Include="CGMHardwareId.h" />
    <ClInclude Include="CGMPetManager.h" />
    <ClInclude Include="ChaosBox.h" />
    <ClInclude Include="ChaosCastle.h" />
    <ClInclude Include="CharacterAdvance.h" />
    <ClInclude Include="ChatManager.h" />
    <ClInclude Include="CGMMixExpansion.h" />
    <ClInclude Include="ComboSkill.h" />
    <ClInclude Include="Command.h" />
    <ClInclude Include="CommandManager.h" />
    <ClInclude Include="Connection.h" />
    <ClInclude Include="CriticalSection.h" />
    <ClInclude Include="Crywolf.h" />
    <ClInclude Include="CrywolfAltar.h" />
    <ClInclude Include="CrywolfObjInfo.h" />
    <ClInclude Include="CrywolfStateTimeInfo.h" />
    <ClInclude Include="CrywolfStatue.h" />
    <ClInclude Include="CrywolfSync.h" />
    <ClInclude Include="CrywolfUtil.h" />
    <ClInclude Include="CSProtocol.h" />
    <ClInclude Include="CustomArena.h" />
    <ClInclude Include="CustomAttack.h" />
    <ClInclude Include="CustomBuyVip.h" />
    <ClInclude Include="CustomCombo.h" />
    <ClInclude Include="CustomCommandDescription.h" />
    <ClInclude Include="CustomDeathMessage.h" />
    <ClInclude Include="CustomEventDrop.h" />
    <ClInclude Include="CustomEventTime.h" />
    <ClInclude Include="CustomExchangeCoin.h" />
    <ClInclude Include="CustomItemUseVip.h" />
    <ClInclude Include="CustomJewel.h" />
    <ClInclude Include="CustomMix.h" />
    <ClInclude Include="CustomMonster.h" />
    <ClInclude Include="CustomMove.h" />
    <ClInclude Include="CustomNpcCollector.h" />
    <ClInclude Include="CustomNpcCommand.h" />
    <ClInclude Include="CustomNpcMove.h" />
    <ClInclude Include="CustomNpcQuest.h" />
    <ClInclude Include="CustomOnlineLottery.h" />
    <ClInclude Include="CustomPick.h" />
    <ClInclude Include="CustomQuest.h" />
    <ClInclude Include="CustomQuiz.h" />
    <ClInclude Include="CustomRanking.h" />
    <ClInclude Include="CustomRankUser.h" />
    <ClInclude Include="CustomStartItem.h" />
    <ClInclude Include="CustomStore.h" />
    <ClInclude Include="CustomTop.h" />
    <ClInclude Include="CustomWing.h" />
    <ClInclude Include="CustomWingMix.h" />
    <ClInclude Include="DarkSpirit.h" />
    <ClInclude Include="DefaultClassInfo.h" />
    <ClInclude Include="define_global.h" />
    <ClInclude Include="DevilSquare.h" />
    <ClInclude Include="DoubleGoer.h" />
    <ClInclude Include="DSProtocol.h" />
    <ClInclude Include="Duel.h" />
    <ClInclude Include="Effect.h" />
    <ClInclude Include="EffectManager.h" />
    <ClInclude Include="ESProtocol.h" />
    <ClInclude Include="EventFindPath.h" />
    <ClInclude Include="EventGvG.h" />
    <ClInclude Include="EventHideAndSeek.h" />
    <ClInclude Include="EventInventory.h" />
    <ClInclude Include="EventKillAll.h" />
    <ClInclude Include="EventPvp.h" />
    <ClInclude Include="EventQuickly.h" />
    <ClInclude Include="EventRunAndCatch.h" />
    <ClInclude Include="EventRussianRoulette.h" />
    <ClInclude Include="EventStart.h" />
    <ClInclude Include="EventTvT.h" />
    <ClInclude Include="ExperienceTable.h" />
    <ClInclude Include="Filter.h" />
    <ClInclude Include="FilterRename.h" />
    <ClInclude Include="Fruit.h" />
    <ClInclude Include="GameMain.h" />
    <ClInclude Include="GameMaster.h" />
    <ClInclude Include="GameServer.h" />
    <ClInclude Include="Gate.h" />
    <ClInclude Include="GensSystem.h" />
    <ClInclude Include="GMHolyItem.h" />
    <ClInclude Include="GuardianStatue.h" />
    <ClInclude Include="Guild.h" />
    <ClInclude Include="GuildClass.h" />
    <ClInclude Include="GuildMatching.h" />
    <ClInclude Include="HackCheck.h" />
    <ClInclude Include="HackPacketCheck.h" />
    <ClInclude Include="HackSkillCheck.h" />
    <ClInclude Include="Helper.h" />
    <ClInclude Include="IllusionTemple.h" />
    <ClInclude Include="ImperialGuardian.h" />
    <ClInclude Include="InvasionManager.h" />
    <ClInclude Include="InventoryEquipment.h" />
    <ClInclude Include="IpManager.h" />
    <ClInclude Include="Item.h" />
    <ClInclude Include="ItemBag.h" />
    <ClInclude Include="ItemBagEx.h" />
    <ClInclude Include="ItemBagManager.h" />
    <ClInclude Include="ItemColorMgr.h" />
    <ClInclude Include="ItemDrop.h" />
    <ClInclude Include="ItemManager.h" />
    <ClInclude Include="ItemMove.h" />
    <ClInclude Include="ItemOption.h" />
    <ClInclude Include="ItemOptionRate.h" />
    <ClInclude Include="ItemStack.h" />
    <ClInclude Include="ItemValue.h" />
    <ClInclude Include="ItemValueTrade.h" />
    <ClInclude Include="JewelMix.h" />
    <ClInclude Include="JewelOfHarmonyOption.h" />
    <ClInclude Include="JewelOfHarmonyType.h" />
    <ClInclude Include="JSProtocol.h" />
    <ClInclude Include="Kalima.h" />
    <ClInclude Include="Kanturu.h" />
    <ClInclude Include="KanturuBattleOfMaya.h" />
    <ClInclude Include="KanturuBattleOfNightmare.h" />
    <ClInclude Include="KanturuBattleStanby.h" />
    <ClInclude Include="KanturuBattleUserMng.h" />
    <ClInclude Include="KanturuEntranceNPC.h" />
    <ClInclude Include="KanturuMaya.h" />
    <ClInclude Include="KanturuMonsterMng.h" />
    <ClInclude Include="KanturuObjInfo.h" />
    <ClInclude Include="KanturuStateInfo.h" />
    <ClInclude Include="KanturuTowerOfRefinement.h" />
    <ClInclude Include="KanturuUtil.h" />
    <ClInclude Include="LifeStone.h" />
    <ClInclude Include="Log.h" />
    <ClInclude Include="LuckyCoin.h" />
    <ClInclude Include="LuckyItem.h" />
    <ClInclude Include="Map.h" />
    <ClInclude Include="MapItem.h" />
    <ClInclude Include="MapManager.h" />
    <ClInclude Include="MapPath.h" />
    <ClInclude Include="MapServerManager.h" />
    <ClInclude Include="MasterResetTable.h" />
    <ClInclude Include="MasterSkillTree.h" />
    <ClInclude Include="MemoryAllocator.h" />
    <ClInclude Include="MemoryAllocatorInfo.h" />
    <ClInclude Include="MemScript.h" />
    <ClInclude Include="Mercenary.h" />
    <ClInclude Include="Message.h" />
    <ClInclude Include="MiniDump.h" />
    <ClInclude Include="MiniMap.h" />
    <ClInclude Include="MiningSystem.h" />
    <ClInclude Include="Monster.h" />
    <ClInclude Include="MonsterAI.h" />
    <ClInclude Include="MonsterAIAgro.h" />
    <ClInclude Include="MonsterAIAutomata.h" />
    <ClInclude Include="MonsterAIAutomataInfo.h" />
    <ClInclude Include="MonsterAIElement.h" />
    <ClInclude Include="MonsterAIElementInfo.h" />
    <ClInclude Include="MonsterAIGroup.h" />
    <ClInclude Include="MonsterAIGroupMember.h" />
    <ClInclude Include="MonsterAIMovePath.h" />
    <ClInclude Include="MonsterAIMovePathInfo.h" />
    <ClInclude Include="MonsterAIRule.h" />
    <ClInclude Include="MonsterAIRuleInfo.h" />
    <ClInclude Include="MonsterAIState.h" />
    <ClInclude Include="MonsterAIUnit.h" />
    <ClInclude Include="MonsterAIUnitInfo.h" />
    <ClInclude Include="MonsterAIUtil.h" />
    <ClInclude Include="MonsterManager.h" />
    <ClInclude Include="MonsterSetBase.h" />
    <ClInclude Include="MonsterSkillElement.h" />
    <ClInclude Include="MonsterSkillElementInfo.h" />
    <ClInclude Include="MonsterSkillElementOption.h" />
    <ClInclude Include="MonsterSkillInfo.h" />
    <ClInclude Include="MonsterSkillManager.h" />
    <ClInclude Include="MonsterSkillUnit.h" />
    <ClInclude Include="MonsterSkillUnitInfo.h" />
    <ClInclude Include="MossMerchant.h" />
    <ClInclude Include="Move.h" />
    <ClInclude Include="MoveSummon.h" />
    <ClInclude Include="ConsoleDebug.h" />
    <ClInclude Include="MultiLanguage.h" />
    <ClInclude Include="MuRummy.h" />
    <ClInclude Include="MuunSystem.h" />
    <ClInclude Include="NewsProtocol.h" />
    <ClInclude Include="Notice.h" />
    <ClInclude Include="NpcTalk.h" />
    <ClInclude Include="ObjectManager.h" />
    <ClInclude Include="PacketManager.h" />
    <ClInclude Include="Party.h" />
    <ClInclude Include="PartyMatching.h" />
    <ClInclude Include="Path.h" />
    <ClInclude Include="PcPoint.h" />
    <ClInclude Include="PentagramSystem.h" />
    <ClInclude Include="PersonalShop.h" />
    <ClInclude Include="Protect.h" />
    <ClInclude Include="Protocol.h" />
    <ClInclude Include="pugiconfig.hpp" />
    <ClInclude Include="pugixml.hpp" />
    <ClInclude Include="Quest.h" />
    <ClInclude Include="QuestObjective.h" />
    <ClInclude Include="QuestReward.h" />
    <ClInclude Include="QuestWorld.h" />
    <ClInclude Include="QuestWorldObjective.h" />
    <ClInclude Include="QuestWorldReward.h" />
    <ClInclude Include="Queue.h" />
    <ClInclude Include="QueueTimer.h" />
    <ClInclude Include="Raklion.h" />
    <ClInclude Include="RaklionBattleOfSelupan.h" />
    <ClInclude Include="RaklionBattleUser.h" />
    <ClInclude Include="RaklionBattleUserMng.h" />
    <ClInclude Include="RaklionObjInfo.h" />
    <ClInclude Include="RaklionSelupan.h" />
    <ClInclude Include="RaklionUtil.h" />
    <ClInclude Include="RandomManager.h" />
    <ClInclude Include="ReadScript2.h" />
    <ClInclude Include="Reconnect.h" />
    <ClInclude Include="CGMBattleIce3.h" />
    <ClInclude Include="ResetTable.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="ScheduleManager.h" />
    <ClInclude Include="SerialCheck.h" />
    <ClInclude Include="ServerDisplayer.h" />
    <ClInclude Include="ServerInfo.h" />
    <ClInclude Include="SetItemOption.h" />
    <ClInclude Include="SetItemType.h" />
    <ClInclude Include="Shop.h" />
    <ClInclude Include="ShopManager.h" />
    <ClInclude Include="Skill.h" />
    <ClInclude Include="SkillDamage.h" />
    <ClInclude Include="SkillHitBox.h" />
    <ClInclude Include="SkillManager.h" />
    <ClInclude Include="SocketItemOption.h" />
    <ClInclude Include="SocketItemType.h" />
    <ClInclude Include="SocketManager.h" />
    <ClInclude Include="SocketManagerUdp.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="SummonScroll.h" />
    <ClInclude Include="ThemidaSDK.h" />
    <ClInclude Include="MyTimer.h" />
    <ClInclude Include="Trade.h" />
    <ClInclude Include="Union.h" />
    <ClInclude Include="UnionInfo.h" />
    <ClInclude Include="User.h" />
    <ClInclude Include="Util.h" />
    <ClInclude Include="Viewport.h" />
    <ClInclude Include="Warehouse.h" />
    <ClInclude Include="WindowsConsole.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\Util\CCRC32.Cpp" />
    <ClCompile Include="..\..\..\Util\Math.cpp" />
    <ClCompile Include="380ItemOption.cpp" />
    <ClCompile Include="380ItemType.cpp" />
    <ClCompile Include="AppointItemDrop.cpp" />
    <ClCompile Include="ArcaBattle.cpp" />
    <ClCompile Include="Attack.cpp" />
    <ClCompile Include="BattleGround.cpp" />
    <ClCompile Include="BattleSoccer.cpp" />
    <ClCompile Include="BattleSoccerManager.cpp" />
    <ClCompile Include="BloodCastle.cpp" />
    <ClCompile Include="BonusManager.cpp" />
    <ClCompile Include="BotBuffer.cpp" />
    <ClCompile Include="CannonTower.cpp" />
    <ClCompile Include="CashShop.cpp" />
    <ClCompile Include="CastleDeep.cpp" />
    <ClCompile Include="CastleSiege.cpp" />
    <ClCompile Include="CastleSiegeCrown.cpp" />
    <ClCompile Include="CastleSiegeCrownSwitch.cpp" />
    <ClCompile Include="CastleSiegeSync.cpp" />
    <ClCompile Include="CastleSiegeWeapon.cpp" />
    <ClCompile Include="CEventName.cpp" />
    <ClCompile Include="CGMEarringManager.cpp" />
    <ClCompile Include="CGMFlagNatManager.cpp" />
    <ClCompile Include="CGMHardwareId.cpp" />
    <ClCompile Include="CGMPetManager.cpp" />
    <ClCompile Include="ChaosBox.cpp" />
    <ClCompile Include="ChaosCastle.cpp" />
    <ClCompile Include="CharacterAdvance.cpp" />
    <ClCompile Include="ChatManager.cpp" />
    <ClCompile Include="CGMMixExpansion.cpp" />
    <ClCompile Include="ComboSkill.cpp" />
    <ClCompile Include="Command.cpp" />
    <ClCompile Include="CommandManager.cpp" />
    <ClCompile Include="Connection.cpp" />
    <ClCompile Include="CriticalSection.cpp" />
    <ClCompile Include="Crywolf.cpp" />
    <ClCompile Include="CrywolfAltar.cpp" />
    <ClCompile Include="CrywolfObjInfo.cpp" />
    <ClCompile Include="CrywolfStateTimeInfo.cpp" />
    <ClCompile Include="CrywolfStatue.cpp" />
    <ClCompile Include="CrywolfSync.cpp" />
    <ClCompile Include="CrywolfUtil.cpp" />
    <ClCompile Include="CSProtocol.cpp" />
    <ClCompile Include="CustomArena.cpp" />
    <ClCompile Include="CustomAttack.cpp" />
    <ClCompile Include="CustomBuyVip.cpp" />
    <ClCompile Include="CustomCombo.cpp" />
    <ClCompile Include="CustomCommandDescription.cpp" />
    <ClCompile Include="CustomDeathMessage.cpp" />
    <ClCompile Include="CustomEventDrop.cpp" />
    <ClCompile Include="CustomEventTime.cpp" />
    <ClCompile Include="CustomExchangeCoin.cpp" />
    <ClCompile Include="CustomItemUseVip.cpp" />
    <ClCompile Include="CustomJewel.cpp" />
    <ClCompile Include="CustomMix.cpp" />
    <ClCompile Include="CustomMonster.cpp" />
    <ClCompile Include="CustomMove.cpp" />
    <ClCompile Include="CustomNpcCollector.cpp" />
    <ClCompile Include="CustomNpcCommand.cpp" />
    <ClCompile Include="CustomNpcMove.cpp" />
    <ClCompile Include="CustomNpcQuest.cpp" />
    <ClCompile Include="CustomOnlineLottery.cpp" />
    <ClCompile Include="CustomPick.cpp" />
    <ClCompile Include="CustomQuest.cpp" />
    <ClCompile Include="CustomQuiz.cpp" />
    <ClCompile Include="CustomRanking.cpp" />
    <ClCompile Include="CustomRankUser.cpp" />
    <ClCompile Include="CustomStartItem.cpp" />
    <ClCompile Include="CustomStore.cpp" />
    <ClCompile Include="CustomTop.cpp" />
    <ClCompile Include="CustomWing.cpp" />
    <ClCompile Include="CustomWingMix.cpp" />
    <ClCompile Include="DarkSpirit.cpp" />
    <ClCompile Include="DefaultClassInfo.cpp" />
    <ClCompile Include="DevilSquare.cpp" />
    <ClCompile Include="DoubleGoer.cpp" />
    <ClCompile Include="DSProtocol.cpp" />
    <ClCompile Include="Duel.cpp" />
    <ClCompile Include="Effect.cpp" />
    <ClCompile Include="EffectManager.cpp" />
    <ClCompile Include="ESProtocol.cpp" />
    <ClCompile Include="EventFindPath.cpp" />
    <ClCompile Include="EventGvG.cpp" />
    <ClCompile Include="EventHideAndSeek.cpp" />
    <ClCompile Include="EventInventory.cpp" />
    <ClCompile Include="EventKillAll.cpp" />
    <ClCompile Include="EventPvP.cpp" />
    <ClCompile Include="EventQuickly.cpp" />
    <ClCompile Include="EventRunAndCatch.cpp" />
    <ClCompile Include="EventRussianRoulette.cpp" />
    <ClCompile Include="EventStart.cpp" />
    <ClCompile Include="EventTvT.cpp" />
    <ClCompile Include="ExperienceTable.cpp" />
    <ClCompile Include="Filter.cpp" />
    <ClCompile Include="FilterRaname.cpp" />
    <ClCompile Include="Fruit.cpp" />
    <ClCompile Include="GameMain.cpp" />
    <ClCompile Include="GameMaster.cpp" />
    <ClCompile Include="GameServer.cpp" />
    <ClCompile Include="Gate.cpp" />
    <ClCompile Include="GensSystem.cpp" />
    <ClCompile Include="GMHolyItem.cpp" />
    <ClCompile Include="GuardianStatue.cpp" />
    <ClCompile Include="Guild.cpp" />
    <ClCompile Include="GuildClass.cpp" />
    <ClCompile Include="GuildMatching.cpp" />
    <ClCompile Include="HackCheck.cpp" />
    <ClCompile Include="HackPacketCheck.cpp" />
    <ClCompile Include="HackSkillCheck.cpp" />
    <ClCompile Include="Helper.cpp" />
    <ClCompile Include="IllusionTemple.cpp" />
    <ClCompile Include="ImperialGuardian.cpp" />
    <ClCompile Include="InvasionManager.cpp" />
    <ClCompile Include="InventoryEquipment.cpp" />
    <ClCompile Include="IpManager.cpp" />
    <ClCompile Include="Item.cpp" />
    <ClCompile Include="ItemBag.cpp" />
    <ClCompile Include="ItemBagEx.cpp" />
    <ClCompile Include="ItemBagManager.cpp" />
    <ClCompile Include="ItemColorMgr.cpp" />
    <ClCompile Include="ItemDrop.cpp" />
    <ClCompile Include="ItemManager.cpp" />
    <ClCompile Include="ItemMove.cpp" />
    <ClCompile Include="ItemOption.cpp" />
    <ClCompile Include="ItemOptionRate.cpp" />
    <ClCompile Include="ItemStack.cpp" />
    <ClCompile Include="ItemValue.cpp" />
    <ClCompile Include="ItemValueTrade.cpp" />
    <ClCompile Include="JewelMix.cpp" />
    <ClCompile Include="JewelOfHarmonyOption.cpp" />
    <ClCompile Include="JewelOfHarmonyType.cpp" />
    <ClCompile Include="JSProtocol.cpp" />
    <ClCompile Include="Kalima.cpp" />
    <ClCompile Include="Kanturu.cpp" />
    <ClCompile Include="KanturuBattleOfMaya.cpp" />
    <ClCompile Include="KanturuBattleOfNightmare.cpp" />
    <ClCompile Include="KanturuBattleStanby.cpp" />
    <ClCompile Include="KanturuBattleUserMng.cpp" />
    <ClCompile Include="KanturuEntranceNPC.cpp" />
    <ClCompile Include="KanturuMaya.cpp" />
    <ClCompile Include="KanturuMonsterMng.cpp" />
    <ClCompile Include="KanturuObjInfo.cpp" />
    <ClCompile Include="KanturuStateInfo.cpp" />
    <ClCompile Include="KanturuTowerOfRefinement.cpp" />
    <ClCompile Include="KanturuUtil.cpp" />
    <ClCompile Include="LifeStone.cpp" />
    <ClCompile Include="Log.cpp" />
    <ClCompile Include="LuckyCoin.cpp" />
    <ClCompile Include="LuckyItem.cpp" />
    <ClCompile Include="Map.cpp" />
    <ClCompile Include="MapItem.cpp" />
    <ClCompile Include="MapManager.cpp" />
    <ClCompile Include="MapPath.cpp" />
    <ClCompile Include="MapServerManager.cpp" />
    <ClCompile Include="MasterResetTable.cpp" />
    <ClCompile Include="MasterSkillTree.cpp" />
    <ClCompile Include="MemoryAllocator.cpp" />
    <ClCompile Include="MemoryAllocatorInfo.cpp" />
    <ClCompile Include="MemScript.cpp" />
    <ClCompile Include="Mercenary.cpp" />
    <ClCompile Include="Message.cpp" />
    <ClCompile Include="MiniDump.cpp" />
    <ClCompile Include="MiniMap.cpp" />
    <ClCompile Include="MiningSystem.cpp" />
    <ClCompile Include="Monster.cpp" />
    <ClCompile Include="MonsterAI.cpp" />
    <ClCompile Include="MonsterAIAgro.cpp" />
    <ClCompile Include="MonsterAIAutomata.cpp" />
    <ClCompile Include="MonsterAIAutomataInfo.cpp" />
    <ClCompile Include="MonsterAIElement.cpp" />
    <ClCompile Include="MonsterAIElementInfo.cpp" />
    <ClCompile Include="MonsterAIGroup.cpp" />
    <ClCompile Include="MonsterAIGroupMember.cpp" />
    <ClCompile Include="MonsterAIMovePath.cpp" />
    <ClCompile Include="MonsterAIMovePathInfo.cpp" />
    <ClCompile Include="MonsterAIRule.cpp" />
    <ClCompile Include="MonsterAIRuleInfo.cpp" />
    <ClCompile Include="MonsterAIState.cpp" />
    <ClCompile Include="MonsterAIUnit.cpp" />
    <ClCompile Include="MonsterAIUnitInfo.cpp" />
    <ClCompile Include="MonsterAIUtil.cpp" />
    <ClCompile Include="MonsterManager.cpp" />
    <ClCompile Include="MonsterSetBase.cpp" />
    <ClCompile Include="MonsterSkillElement.cpp" />
    <ClCompile Include="MonsterSkillElementInfo.cpp" />
    <ClCompile Include="MonsterSkillElementOption.cpp" />
    <ClCompile Include="MonsterSkillInfo.cpp" />
    <ClCompile Include="MonsterSkillManager.cpp" />
    <ClCompile Include="MonsterSkillUnit.cpp" />
    <ClCompile Include="MonsterSkillUnitInfo.cpp" />
    <ClCompile Include="MossMerchant.cpp" />
    <ClCompile Include="Move.cpp" />
    <ClCompile Include="MoveSummon.cpp" />
    <ClCompile Include="ConsoleDebug.cpp" />
    <ClCompile Include="MultiLanguage.cpp" />
    <ClCompile Include="MuRummy.cpp" />
    <ClCompile Include="MuunSystem.cpp" />
    <ClCompile Include="Notice.cpp" />
    <ClCompile Include="NpcTalk.cpp" />
    <ClCompile Include="ObjectManager.cpp" />
    <ClCompile Include="PacketManager.cpp" />
    <ClCompile Include="Party.cpp" />
    <ClCompile Include="PartyMatching.cpp" />
    <ClCompile Include="Path.cpp" />
    <ClCompile Include="PcPoint.cpp" />
    <ClCompile Include="PentagramSystem.cpp" />
    <ClCompile Include="PersonalShop.cpp" />
    <ClCompile Include="Protect.cpp" />
    <ClCompile Include="Protocol.cpp" />
    <ClCompile Include="pugixml.cpp" />
    <ClCompile Include="Quest.cpp" />
    <ClCompile Include="QuestObjective.cpp" />
    <ClCompile Include="QuestReward.cpp" />
    <ClCompile Include="QuestWorld.cpp" />
    <ClCompile Include="QuestWorldObjective.cpp" />
    <ClCompile Include="QuestWorldReward.cpp" />
    <ClCompile Include="Queue.cpp" />
    <ClCompile Include="QueueTimer.cpp" />
    <ClCompile Include="Raklion.cpp" />
    <ClCompile Include="RaklionBattleOfSelupan.cpp" />
    <ClCompile Include="RaklionBattleUser.cpp" />
    <ClCompile Include="RaklionBattleUserMng.cpp" />
    <ClCompile Include="RaklionObjInfo.cpp" />
    <ClCompile Include="RaklionSelupan.cpp" />
    <ClCompile Include="RaklionUtil.cpp" />
    <ClCompile Include="RandomManager.cpp" />
    <ClCompile Include="Reconnect.cpp" />
    <ClCompile Include="CGMBattleIce3.cpp" />
    <ClCompile Include="ResetTable.cpp" />
    <ClCompile Include="ScheduleManager.cpp" />
    <ClCompile Include="SerialCheck.cpp" />
    <ClCompile Include="ServerDisplayer.cpp" />
    <ClCompile Include="ServerInfo.cpp" />
    <ClCompile Include="SetItemOption.cpp" />
    <ClCompile Include="SetItemType.cpp" />
    <ClCompile Include="Shop.cpp" />
    <ClCompile Include="ShopManager.cpp" />
    <ClCompile Include="Skill.cpp" />
    <ClCompile Include="SkillDamage.cpp" />
    <ClCompile Include="SkillHitBox.cpp" />
    <ClCompile Include="SkillManager.cpp" />
    <ClCompile Include="SocketItemOption.cpp" />
    <ClCompile Include="SocketItemType.cpp" />
    <ClCompile Include="SocketManager.cpp" />
    <ClCompile Include="SocketManagerUdp.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_EX603|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_EX603CS|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="SummonScroll.cpp" />
    <ClCompile Include="MyTimer.cpp" />
    <ClCompile Include="Trade.cpp" />
    <ClCompile Include="Union.cpp" />
    <ClCompile Include="UnionInfo.cpp" />
    <ClCompile Include="User.cpp" />
    <ClCompile Include="Util.cpp" />
    <ClCompile Include="Viewport.cpp" />
    <ClCompile Include="Warehouse.cpp" />
    <ClCompile Include="WindowsConsole.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="GameServer.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>