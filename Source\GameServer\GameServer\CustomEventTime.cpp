#include "stdafx.h"
#include "DSProtocol.h"
#include "GameMain.h"
#include "ServerDisplayer.h"
#include "Util.h"
#include "Message.h"
#include "User.h"
#include "Path.h"
#include "ServerInfo.h"
#include "CustomEventTime.h"
#include "MemScript.h"
#include "CEventName.h"
#include "Notice.h"
#include <string.h>

CCustomEventTime gCustomEventTime;


void CCustomEventTime::GCReqEventTime(int Index, PMSG_CUSTOM_EVENTTIME_RECV* lpMsg)
{

#if (GAMESERVER_CLIENTE_UPDATE >= 8)

	if (gServerInfo.m_CustomEventTimeSwitch == 0)
	{
		return;
	}

	if (gObjIsConnected(Index) == false)
	{
		return;
	}

	BYTE send[4096];

	PMSG_CUSTOM_EVENTTIME_SEND pMsg;

	pMsg.header.set(0xF3, 0xE8, 0);

	int size = sizeof(pMsg);

	pMsg.count = 0;

	CUSTOM_EVENTTIME_DATA info;

	for (int n = 0; n < 20; n++)
	{
		info.index = n;

		info.time = gEventName.GlobalRemainTime(n);
	
		strncpy(info.name, gMessage.GlobalText(gEventName.GlobalNameID(n), gObj[Index].LanguageCode), sizeof(info.name) - 1);
		
		pMsg.count++;

		memcpy(&send[size], &info, sizeof(info));
		size += sizeof(info);
	}

	for (int n = 0; n < 20; n++)
	{
		info.index = n + 20;

		info.time = gEventName.InvasionRemainTime(n);

		strncpy(info.name, gMessage.GlobalText(gEventName.InvasionNameID(n), gObj[Index].LanguageCode), sizeof(info.name) - 1);
	
		pMsg.count++;

		memcpy(&send[size], &info, sizeof(info));
		size += sizeof(info);
	}

	for (int n = 0; n < 20; n++)
	{
		info.index = n + 40;

		info.time = gEventName.ArenaRemainTime(n);

		strncpy(info.name, gMessage.GlobalText(gEventName.ArenaNameID(n), gObj[Index].LanguageCode), sizeof(info.name) - 1);
		
		pMsg.count++;

		memcpy(&send[size], &info, sizeof(info));
		size += sizeof(info);
	}

	pMsg.header.size[0] = SET_NUMBERHB(size);
	pMsg.header.size[1] = SET_NUMBERLB(size);
	// ---
	memcpy(send, &pMsg, sizeof(pMsg));

	DataSend(Index, send, size);

#endif
	return;
}