#pragma once

#include "Protocol.h"
#include "User.h"

struct MONSTER_SET_BASE_INFO;

extern void gObjMonsterDieGiveItem(LPOBJ lpObj, LPOBJ lpTarget);
extern bool gObjSetPosMonster(int aIndex, MONSTER_SET_BASE_INFO* MonsterRef);
extern bool gObjSetMonster(int aIndex, int MonsterClass);
extern bool gObjMonsterRegen(LPOBJ lpObj);
extern bool gObjMonsterMoveCheck(LPOBJ lpObj, int tx, int ty);
extern void gObjMonsterInitHitDamage(LPOBJ lpObj);
extern void gObjMonsterSetHitDamage(LPOBJ lpObj, int aIndex, int damage);
extern int gObjMonsterDelHitDamageUser(LPOBJ lpObj);
extern int gObjMonsterGetTopHitDamageUser(LPOBJ lpObj);
extern int gObjMonsterGetTopHitDamageParty(LPOBJ lpObj, int PartyNumber, int* TopHitDamageUser);
extern int gObjAddMonster(int map);
extern int gObjAddSummon();
extern int gObjSummonSetEnemy(LPOBJ lpObj, int aIndex);
extern void gObjSummonKill(int aIndex);
extern void gObjMonsterMagicAttack(LPOBJ lpObj, int skill);
extern void gObjMonsterBeattackRecv(BYTE* aRecv, int aIndex);
extern void gObjMonsterAttack(LPOBJ lpObj, LPOBJ lpTarget);
extern void gObjMonsterDie(LPOBJ lpObj, LPOBJ lpTarget);
extern void gObjMonsterStateProc(LPOBJ lpObj, int code, int aIndex, int SubCode);

//**************************************************************************//
// RAW FUNCTIONS ***********************************************************//
//**************************************************************************//

struct PMSG_MAGICATTACK_RESULT
{
	PBMSG_HEAD h;	// C3:19
	BYTE MagicNumberH;	// 3
	BYTE MagicNumberL;	// 4
	BYTE SourceNumberH;	// 5
	BYTE SourceNumberL;	// 6
	BYTE TargetNumberH;	// 7
	BYTE TargetNumberL;	// 8
};

struct PMSG_MAGICATTACK
{
	PBMSG_HEAD h;
	BYTE MagicNumberH;	// 3
	BYTE MagicNumberL;	// 4
	BYTE NumberH;	// 5
	BYTE NumberL;	// 6
	BYTE Dis;	// 7
};

struct PMSG_BEATTACK_COUNT
{
	PBMSG_HEAD h;	// C1:D7
	BYTE MagicNumberH;	// 3
	BYTE MagicNumberL;	// 4
	BYTE X;	// 5
	BYTE Y;	// 6
	BYTE Serial;	// 7
	BYTE Count;	// 8
};

struct PMSG_BEATTACK
{
	BYTE NumberH;	// 0
	BYTE NumberL;	// 1
	BYTE MagicKey;	// 2
};

struct PMSG_DURATION_MAGIC_RECV
{
	PBMSG_HEAD h;
	BYTE MagicNumberH;	// 3
	BYTE MagicNumberL;	// 4
	BYTE X;	// 5
	BYTE Y;	// 6
	BYTE Dir;	// 7
	BYTE Dis;	// 8
	BYTE TargetPos;	// 9
	BYTE NumberH;	// A
	BYTE NumberL;	// B
	BYTE MagicKey;	// C
};

struct PMSG_ATTACK
{
	PBMSG_HEAD h;
	BYTE NumberH;	// 3
	BYTE NumberL;	// 4
	BYTE AttackAction;	// 5
	BYTE DirDis;	// 6
};

struct PMSG_MOVE
{
	PBMSG_HEAD h;	// C1:1D
	BYTE X;	// 3
	BYTE Y;	// 4
	BYTE Path[8];	// 5
};

int gObjMonsterViewportIsCharacter(LPOBJ lpObj);
BOOL gObjMonsterGetTargetPos(LPOBJ lpObj);
BOOL gObjGetTargetPos(LPOBJ lpObj, int sx, int sy, int& tx, int& ty);
int gObjMonsterSearchEnemy(LPOBJ lpObj, BYTE objtype);
int gObjGuardSearchEnemy(LPOBJ lpObj);
void gObjMonsterProcess(LPOBJ lpObj);
BOOL PathFindMoveMsgSend(LPOBJ lpObj);
bool gObjSetBots(int aIndex, int MonsterClass); //MC bot
void gObjMonsterMoveAction(LPOBJ lpObj);
void gObjMonsterBaseAct(LPOBJ lpObj);
void gObjTrapAttackEnemySearchX(LPOBJ lpObj, int count);
void gObjTrapAttackEnemySearchY(LPOBJ lpObj, int count);
void gObjTrapAttackEnemySearch(LPOBJ lpObj);
void gObjTrapAttackEnemySearchRange(LPOBJ lpObj, int iRange);
void gObjMonsterTrapAct(LPOBJ lpObj);
void gObjMonsterStateProcCase0(LPOBJ lpObj, int aIndex);
