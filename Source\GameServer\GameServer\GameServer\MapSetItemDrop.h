#pragma once

#include "User.h"
#include <map>
#include <vector>
#include <string>

// Map-specific Set Item Drop System
// This system reads map-level rules and group item definitions from
// Data\ItemDrop\ItemDropForMap.txt and performs configurable drops.

struct MAP_SET_DROP_CONFIG {
    int mapIndex;               // Map id
    int useGroupNumber;         // Group id to use
    int dropAllSets;            // 1: drop all items in group; 0: drop single
    int ownershipType;          // 0: last-hit, 1: max-damage
    int addGroup;               // Additional option group id (from ItemDropMainData.txt)
    int excCountGroup;          // Excellent count group id
    int weaponExcGroup;         // Excellent attribute group for weapons/necklace
    int armorExcGroup;          // Excellent attribute group for armors/rings
    int individualRate;         // Overall drop chance (1..1,000,000). 0 => per-item probability mode
    int noDropSetGroup;         // Apply ItemSetNotDrop.txt group (1..10), 0 means not applied
};

struct DROP_SET_ITEM_INFO {
    int group;                  // Group id
    int category1;              // Item Category
    int category2;              // Item Index
    int minLevel;               // Min level (0..15, 255 => use original system)
    int maxLevel;               // Max level (0..15, 255 => use original system)
    int skill;                  // Skill probability (0..100, 100 => original system)
    int luck;                   // Luck probability (0..100, 100 => original system)
    int addition;               // Add option: 0 off, 1..7 random +0..+28, 8 fixed +28, 11..17 fixed +4..+28, 3 => original system
    int durability;             // Durability (0..255). Typically 0 to use server default
    int excellent;              // Excellent: 0 off, 1..6 random count, 7 full, 101..163 fixed mask, 255 => original system
    int setValue;               // Ancient set value: 0 off, 1 random ancient, 2 default (5/9), 3 enhanced (6/10), 5/6/9/10 fixed
    int probability;            // Probability (1..1,000,000)
};

struct DROP_SET_GROUP_INFO {
    int groupIndex;
    std::vector<DROP_SET_ITEM_INFO> items;
    // Precomputed for weighted random selection (when individualRate > 0)
    int totalWeight = 0;
};

class CMapSetItemDrop {
public:
    CMapSetItemDrop();
    ~CMapSetItemDrop();

    bool Load(const char* filePath);                // Load configuration file
    void Clear();

    bool IsMapEnabled(int mapIndex) const;

    // Main API: perform a map-specific set item drop
    // lpLastHitPlayer is the actual last-hit player preserved by caller
    bool DropForMap(int mapIndex, LPOBJ lpMonster, LPOBJ lpLastHitPlayer, int x, int y);

private:
    // Parsing helpers
    bool ParseFile(const char* filePath);
    void FinalizeGroups();

    // Selection and item creation
    bool DropAllInGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config);
    bool DropSingleFromGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config);
    bool RollPerItemInGroup(LPOBJ lpOwner, const DROP_SET_GROUP_INFO& group, int mapIndex, int x, int y, const MAP_SET_DROP_CONFIG& config);
    bool CreateAndDropItem(LPOBJ lpOwner, int map, int x, int y, const DROP_SET_ITEM_INFO& def, const DROP_SET_ITEM_INFO* firstOfGroup);

    // Option calculations
    BYTE CalcItemLevel(const DROP_SET_ITEM_INFO& def);
    void CalcOptions(const DROP_SET_ITEM_INFO& def, WORD itemIndex,
                     BYTE& optSkill, BYTE& optLuck, BYTE& optAdd, BYTE& newOption,
                     BYTE& setOption, BYTE socketOption[MAX_SOCKET_OPTION]);

    // Utils
    static bool IsTwoHanded(WORD itemIndex);
    static WORD MakeItemIndex(int cat, int idx);

private:
    bool m_loaded;
    std::map<int, MAP_SET_DROP_CONFIG> m_mapConfigs;   // mapIndex -> config
    std::map<int, DROP_SET_GROUP_INFO> m_groups;       // groupIndex -> items
};

extern CMapSetItemDrop gMapSetItemDrop;



