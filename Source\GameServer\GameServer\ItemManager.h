// ItemManager.h: interface for the CItemManager class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "DefaultClassInfo.h"
#include "Protocol.h"
#include "User.h"

constexpr int MAX_ITEM_INFO										= 12;
constexpr int MAX_ITEM_SECTION									= 16;
constexpr int MAX_ITEM_TYPE										= 512;
constexpr int MAX_ITEM											= (MAX_ITEM_SECTION * MAX_ITEM_TYPE);
constexpr int ITEM_INVALID										= -1;

constexpr int REQUEST_EQUIPMENT_INVENTORY						= 0;
constexpr int REQUEST_EQUIPMENT_TRADE							= 1;
constexpr int REQUEST_EQUIPMENT_STORAGE							= 2;
constexpr int REQUEST_EQUIPMENT_CHAOS_MIX						= 3;
constexpr int REQUEST_EQUIPMENT_MYSHOP							= 4;
constexpr int REQUEST_EQUIPMENT_TRAINER_MIX						= 5;
constexpr int REQUEST_EQUIPMENT_ELPIS_MIX						= 6;
constexpr int REQUEST_EQUIPMENT_OSBOURNE_MIX					= 7;
constexpr int REQUEST_EQUIPMENT_JERRIDON_MIX					= 8;
constexpr int REQUEST_EQUIPMENT_CHAOS_CARD_MIX					= 9;
constexpr int REQUEST_EQUIPMENT_CHERRYBLOSSOM_MIX				= 10;
constexpr int REQUEST_EQUIPMENT_EXTRACT_SEED_MIX				= 11;
constexpr int REQUEST_EQUIPMENT_SEED_SPHERE_MIX					= 12;
constexpr int REQUEST_EQUIPMENT_ATTACH_SOCKET_MIX				= 13;
constexpr int REQUEST_EQUIPMENT_DETACH_SOCKET_MIX				= 14;

constexpr bool in_range_exclusive(int x, int begin, int end) noexcept {
	return x >= begin && x < end;
}

constexpr int GET_ITEM(int x, int y) noexcept {
	return (x * MAX_ITEM_TYPE) + y;
}

constexpr int CHECK_ITEM(int x) noexcept {
	return in_range_exclusive(x, 0, MAX_ITEM) ? x : ITEM_INVALID;
}

constexpr bool INVENTORY_WEAR_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, 0, INVENTORY_WEAR_SIZE);
}

constexpr bool INVENTORY_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, 0, INVENTORY_SIZE);
}

constexpr bool INVENTORY_FULL_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, 0, INVENTORY_EXT4_SIZE);
}

constexpr bool INVENTORY_BASE_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_WEAR_SIZE, INVENTORY_SIZE);
}

constexpr bool INVENTORY_MAIN_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_WEAR_SIZE, INVENTORY_MAIN_SIZE);
}

constexpr bool INVENTORY_EXT1_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_MAIN_SIZE, INVENTORY_EXT1_SIZE);
}

constexpr bool INVENTORY_EXT2_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_EXT1_SIZE, INVENTORY_EXT2_SIZE);
}

constexpr bool INVENTORY_EXT3_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_EXT2_SIZE, INVENTORY_EXT3_SIZE);
}

constexpr bool INVENTORY_EXT4_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_EXT3_SIZE, INVENTORY_EXT4_SIZE);
}

constexpr bool INVENTORY_SHOP_RANGE(int offsetIndex) noexcept {
	return in_range_exclusive(offsetIndex, INVENTORY_EXT4_SIZE, INVENTORY_FULL_SIZE);
}

#define WAREHOUSE_RANGE(x)				(((x)<0)?0:((x)>=WAREHOUSE_SIZE)?0:1)
#define WAREHOUSE_MAIN_RANGE(x)			(((x)<0)?0:((x)>=WAREHOUSE_EXT1_SIZE)?0:1)
#define WAREHOUSE_EXT1_RANGE(x)			(((x)<WAREHOUSE_EXT1_SIZE)?0:((x)>=WAREHOUSE_SIZE)?0:1)
#define TRADE_RANGE(x)					((x>=0&&x<TRADE_SIZE)?1:0)
#define CHAOS_BOX_RANGE(x)				((x>=0&&x<CHAOS_BOX_SIZE)?1:0)

#define NAME_CLASS_ARRAY {"DarkWizard", "DarkKnight", "FairyElf", "MagicGladiator", "DarkLord", "Summoner", "RageFighter", "GrowLancer", "RuneWizard", "Slayer", "GunCrusher", "Kundun", "LemuriaMage", "IllusionKnight", "Alquimista"}
#define NAME_CLASS_ARRAY2 {"DW", "DK", "FE", "MG", "DL", "SM", "RG", "GL", "RW", "SL", "GC", "KN", "LM", "IK", "AQ"}

//**********************************************//
//************ Client -> GameServer ************//
//**********************************************//

struct PMSG_ITEM_GET_RECV
{
	PBMSG_HEAD header; // C1:22
	uint8_t index[2];
};

struct PMSG_ITEM_DROP_RECV
{
	PBMSG_HEAD header; // C1:23
	uint8_t x;
	uint8_t y;
	UInt16 slotIndex;
};

struct PMSG_ITEM_MOVE_RECV
{
	PBMSG_HEAD header; // C1:24
	uint8_t SourceFlag;
	uint8_t TargetFlag;
	UInt16 SourceSlot;
	UInt16 TargetSlot;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_ITEM_USE_RECV
{
	PBMSG_HEAD header; // C1:26
	uint8_t SourceSlot;
	uint8_t TargetSlot;
	uint8_t type;
};

struct PMSG_ITEM_BUY_RECV
{
	PBMSG_HEAD header; // C1:32
	uint8_t slot;
};

struct PMSG_ITEM_SELL_RECV
{
	PBMSG_HEAD header; // C1:33
	uint8_t slot;
};

struct PMSG_ITEM_REPAIR_RECV
{
	PBMSG_HEAD header; // C1:34
	uint8_t slot;
	uint8_t type;
};

//**********************************************//
//************ GameServer -> Client ************//
//**********************************************//
#ifdef INVENTORY_EXT_BUY
struct PMSG_CREATE_INVENTARIO_SEND
{
	PSBMSG_HEAD header; // C3:22
	uint8_t ExtInventory;
	DWORD InventoryExtPrice;
};
#endif // INVENTORY_EXT_BUY

struct PMSG_ITEM_GET_SEND
{
	PBMSG_HEAD header; // C3:22
	uint8_t result;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_ITEM_DROP_SEND
{
	PBMSG_HEAD header; // C1:23
	uint8_t result;
	UInt16 slotIndex;
};

struct PMSG_ITEM_MOVE_SEND
{
	PBMSG_HEAD header; // C3:24
	uint8_t SubCode;
	UInt16 slotIndex;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_ITEM_CHANGE_SEND
{
	PBMSG_HEAD header; // C1:25
	UInt16 index;
	UInt16 slotIndex;
	uint8_t ItemInfo[MAX_ITEM_INFO];
#if(GAMESERVER_UPDATE>=701)
	uint8_t attribute;
#endif
};

struct PMSG_ITEM_DELETE_SEND
{
	PBMSG_HEAD header; // C1:28
	uint8_t flag;
	UInt16 slot;
};

struct PMSG_ITEM_DUR_SEND
{
	PBMSG_HEAD header; // C1:2A
	UInt16 slot;
	uint8_t dur;
	uint8_t flag;
};

struct PMSG_ITEM_BUY_SEND
{
	PBMSG_HEAD header; // C1:32
	uint8_t result;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_ITEM_SELL_SEND
{
	PBMSG_HEAD header; // C1:33
	uint8_t result;
	DWORD money;
};

struct PMSG_ITEM_REPAIR_SEND
{
	PBMSG_HEAD header; // C1:34
	DWORD money;
};

struct PMSG_ITEM_LIST_SEND
{
	PSWMSG_HEAD header; // C4:F3:10
	uint8_t count;
};

struct PMSG_ITEM_LIST
{
	UInt16 slot;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_ITEM_EQUIPMENT_SEND
{
	PSBMSG_HEAD header; // C1:F3:13
	uint8_t index[2];
	DWORD Equipment[EQUIPMENT_NEW_LENGTH];
};

struct PMSG_ITEM_MODIFY_SEND
{
	PSBMSG_HEAD header; // C1:F3:14
	UInt16 slotIndex;
	uint8_t ItemInfo[MAX_ITEM_INFO];
};

typedef struct
{
	PSBMSG_HEAD  header;
	uint8_t          ID[10];
	uint8_t          Item[MAX_ITEM_INFO];
} PHEADER_DEFAULT_SUBCODE_CHAT_ITEM, * LPPHEADER_DEFAULT_SUBCODE_CHAT_ITEM;

struct PMSG_ITEM_BUY_NEW
{
	PSBMSG_HEAD header; // C1:32
	uint8_t slot;
};

//**********************************************//
//**********************************************//
//**********************************************//
enum cStatType
{
	STRENGTH,
	AGILITY,
	VITALITY,
	ENERGY,
	LEADERSHIP,
	MAX_STAT_TYPE
};

struct ITEM_INFO
{
	int Index;
	//char Path[MAX_PATH];
	char Model[MAX_PATH];
	std::string Name;
	uint8_t Kind1;
	uint8_t Kind2;
	uint8_t Kind3;
	bool TwoHand;
	int Level;
	int Slot;
	int m_wSkillIndex;
	uint8_t Width;
	uint8_t Height;
	uint8_t DropItem;
	DWORD DamageMin;
	DWORD DamageMax;
	int MagicDamageRate;
	int Defense;
	int MagicDefense;
	int DefenseSuccessRate;
	uint8_t AttackSpeed;
	uint8_t WalkSpeed;
	int Durability;
	int MagicDurability;
	int Value;
	int BuyMoney;
	uint8_t Resistance[MAX_RESISTANCE_TYPE];
	DWORD RequireLevel;
	DWORD RequireStrength;
	DWORD RequireDexterity;
	DWORD RequireEnergy;
	DWORD RequireVitality;
	DWORD RequireLeadership;
	uint8_t RequireClass[MAX_CLASS];
	//--
	int SkillIndex;
	//--
	//bool DropItem;
	//bool Trade;
	//bool StorePersonal;
	//bool WhareHouse;
	//bool SellNpc;
	//bool Expensive;
	//bool Repair;
	//WORD Overlap;
	//WORD PcFlag;
	//WORD MuunFlag;
	//bool Dropinventory;
	//uint8_t arg_683;
	//uint8_t arg_684;
	DWORD PowerATTK;
	//WORD arg_688;
	//WORD arg_690;
	const char* GetName() {
		return this->Name.data();
	}
};

class Script_Item //-- item bmd
{
public:
	/*+000*/ int Type;
	/*+004*/ short Index;
	/*+006*/ short SubIndex;
	/*+528*/ char Name[64];
	/*+592*/ uint8_t Kind1;
	/*+593*/ uint8_t Kind2;
	/*+594*/ uint8_t Kind3;
	/*+595*/ bool TwoHand;
	/*+596*/ WORD Level;
	/*+598*/ uint8_t m_byItemSlot;
	/*+600*/ WORD m_wSkillIndex;
	/*+602*/ uint8_t Width;
	/*+603*/ uint8_t Height;
	/*+604*/ WORD DamageMin;
	/*+606*/ WORD DamageMax;
	/*+608*/ uint8_t SuccessfulBlocking;
	/*+610*/ WORD Defense;
	/*+612*/ WORD MagicDefense;
	/*+614*/ uint8_t WeaponSpeed;
	/*+615*/ uint8_t WalkSpeed;
	/*+616*/ uint8_t Durability;
	/*+617*/ uint8_t MagicDurability;
	/*+620*/ DWORD MagicPower;
	/*+624*/ WORD RequireStrength;
	/*+626*/ WORD RequireDexterity;
	/*+628*/ WORD RequireEnergy;
	/*+630*/ WORD RequireVitality;
	/*+632*/ WORD RequireCharisma;
	/*+634*/ WORD RequireLevel;
	/*+636*/ uint8_t Value;
	/*+640*/ int iZen;
	/*+644*/ uint8_t AttType;
	/*+645*/ uint8_t RequireClass[MAX_CLASS];
	/*+659*/ uint8_t Resistance[MAX_RESISTANCE_TYPE];
	/*+667*/ bool Dropinventory;
	/*+668*/ bool Trade;
	/*+669*/ bool StorePersonal;
	/*+670*/ bool WhareHouse;
	/*+671*/ bool SellNpc;
	/*+672*/ bool Expensive;
	/*+673*/ bool Repair;
	/*+676*/ WORD Overlap;
	/*+678*/ WORD PcFlag;
	/*+680*/ WORD MuunFlag;
	/*+686*/ DWORD PowerATTK;
};

typedef std::map<int, ITEM_INFO> type_map_item;

class CItemManager
{
public:
	CItemManager();
	virtual ~CItemManager();
	bool LoadFiles(const std::string& filename);
	bool ReadFileTxt(const std::string& filename);
	bool ReadFileXml(const std::string& filename, bool& is_open);
	//void ExportConfigPet();
#ifndef GAMESERVER_EDITH_EXPORT
	void ExportTXT(char* filename);
#endif // GAMESERVER_CLIENTE_UPDATE >= 16
	void ExportBMD(char* filename);
	void ExportModel(char* filename);

	bool GetInfo(int index, ITEM_INFO* lpInfo);
	int GetItemSkill(int index);
	int CheckItemSkill(int index);
	int GetItemTwoHand(int index);
	int AdquireSkill(int Itemindex);
	bool IsInventoryItem(int Itemindex);
	int GetItemDurability(int index, int level, int NewOption, int SetOption);
	int GetItemRepairMoney(CItem* lpItem, int type);
	int GetInventoryItemSlot(LPOBJ lpObj, int index, int level);
	int GetInventoryItemCount(LPOBJ lpObj, int index, int level);
	int GetInventoryEmptySlotCount(LPOBJ lpObj);
	int GetInventoryMaxValue(LPOBJ lpObj);
	bool CheckItemMake(int index);
	bool CheckItemRequireLevel(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireStrength(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireDexterity(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireVitality(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireEnergy(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireLeadership(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemRequireClass(LPOBJ lpObj, int index);
	bool CheckItemMoveToInventory(LPOBJ lpObj, CItem* lpItem, int slot);
	bool CheckItemMoveToTrade(LPOBJ lpObj, CItem* lpItem, uint8_t TargetFlag);
	bool CheckItemMoveToVault(LPOBJ lpObj, CItem* lpItem, uint8_t TargetFlag);
	bool CheckItemMoveToChaos(LPOBJ lpObj, CItem* lpItem, uint8_t TargetFlag);
#ifdef CHAOS_MACHINE_EXTENSION
	bool CheckItemMoveToMixExpansion(LPOBJ lpObj, CItem* lpItem, uint8_t TargetFlag);
#endif // CHAOS_MACHINE_EXTENSION

	bool CheckItemMoveToBlock(LPOBJ lpObj, CItem* lpItem);
	bool CheckItemInventorySpace(LPOBJ lpObj, int index);
	bool CheckItemInventorySpace(LPOBJ lpObj, int index, int count, bool copy, bool copy2);
	bool CheckItemInventorySpace(uint8_t* InventoryMap, int MaxY, int width, int height);
	bool CheckItemInventorySpace(LPOBJ lpObj, int width, int height);
	//-----------
	void InventoryItemSet(int aIndex, int slot, uint8_t type);
	void InventoryItemSet(uint8_t* InventoryMap, int slot, int width, int height, uint8_t type);
	uint16_t InventoryRectCheck(int aIndex, int x, int y, int width, int height);
	uint16_t InventoryRectCheck(uint8_t* InventoryMap, int x, int y, int width, int height);
	uint16_t InventoryInsertItem(int aIndex, CItem item);
	uint16_t InventoryAddItem(int aIndex, CItem item, int slot);
	void InventoryDelItem(int aIndex, int slot);
	bool InventoryBreakItemStack(LPOBJ lpObj, CItem* lpItem, int slot);
	bool InventoryInsertItemStack(LPOBJ lpObj, CItem* lpItem);
	bool InventoryAddItemStack(LPOBJ lpObj, int SourceSlot, int TargetSlot);
	//-----------
	void TradeItemSet(int aIndex, int slot, uint8_t type);
	uint8_t TradeRectCheck(int aIndex, int x, int y, int width, int height);
	uint8_t TradeInsertItem(int aIndex, CItem item);
	uint8_t TradeAddItem(int aIndex, CItem item, int slot);
	void TradeDelItem(int aIndex, int slot);
	//-----------
	void WarehouseItemSet(int aIndex, int slot, uint8_t type);
	uint8_t WarehouseRectCheck(int aIndex, int x, int y, int width, int height);
	uint8_t WarehouseInsertItem(int aIndex, CItem item);
	uint8_t WarehouseAddItem(int aIndex, CItem item, int slot);
	void WarehouseDelItem(int aIndex, int slot);
	//-----------
	void ChaosBoxItemSet(int aIndex, int slot, uint8_t type);
	uint8_t ChaosBoxRectCheck(int aIndex, int x, int y, int width, int height);
	uint8_t ChaosBoxInsertItem(int aIndex, CItem item);
	uint8_t ChaosBoxAddItem(int aIndex, CItem item, int slot);
	void ChaosBoxDelItem(int aIndex, int slot);
	//-----------
#ifdef CHAOS_MACHINE_EXTENSION
	void MixExpansionItemSet(int aIndex, int slot, uint8_t type);
	uint8_t MixExpansionRectCheck(int aIndex, int x, int y, int width, int height);
	uint8_t MixExpansionInsertItem(int aIndex, CItem item);
	uint8_t MixExpansionAddItem(int aIndex, CItem item, int slot);
	void MixExpansionDelItem(int aIndex, int slot);
#endif // CHAOS_MACHINE_EXTENSION
	//-----------
	void ItemByteConvert(uint8_t* lpMsg, CItem item);
	void DBItemByteConvert(uint8_t* lpMsg, CItem* lpItem);
	bool ConvertItemByte(CItem* lpItem, uint8_t* lpMsg);
	bool IsValidItem(LPOBJ lpObj, CItem* lpItem);
	void UpdateInventoryViewport(int aIndex, int slot);
	void DeleteInventoryItemCount(LPOBJ lpObj, int index, int level, int count);
	void DecreaseItemDur(LPOBJ lpObj, int slot, int dur);
	int RepairItem(LPOBJ lpObj, CItem* lpItem, int slot, int type);
	uint8_t MoveItemToInventoryFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToInventoryFromTrade(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToInventoryFromWarehouse(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToInventoryFromChaosBox(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToInventoryFromPersonalShop(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToTradeFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToTradeFromTrade(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToTradeFromEventInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToWarehouseFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToWarehouseFromWarehouse(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToChaosBoxFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToChaosBoxFromChaosBox(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToPersonalShopFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToPersonalShopFromPersonalShop(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToEventInventoryFromTrade(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToEventInventoryFromEventInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToMuunInventoryFromMuunInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);

#ifdef CHAOS_MACHINE_EXTENSION
	uint8_t MoveItemToMixExpansionFromInventory(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
	uint8_t MoveItemToInventoryFromMixExpansion(LPOBJ lpObj, uint16_t SourceSlot, uint16_t TargetSlot, uint8_t TargetFlag);
#endif // CHAOS_MACHINE_EXTENSION

	bool use(LPOBJ lpObj, CItem* lpItem, uint16_t iSourceIndex, uint16_t iTargetIndex);

#ifdef INVENTORY_EXT_BUY
	void CGInventoryBuyRecv(PMSG_ITEM_BUY_RECV* lpMsg, int aIndex);
#endif // INVENTORY_EXT_BUY

	void CGItemGetRecv(PMSG_ITEM_GET_RECV* lpMsg, int aIndex);
	void CGItemDropRecv(PMSG_ITEM_DROP_RECV* lpMsg, int aIndex);
	bool CGPkDrop(PMSG_ITEM_DROP_RECV* lpMsg, int aIndex);
	void CGItemMoveRecv(PMSG_ITEM_MOVE_RECV* lpMsg, int aIndex);
	void CGItemUseRecv(PMSG_ITEM_USE_RECV* lpMsg, int aIndex);
	void CGItemBuyRecv(PMSG_ITEM_BUY_RECV* lpMsg, int aIndex, int ok);
	void CGItemSellRecv(PMSG_ITEM_SELL_RECV* lpMsg, int aIndex);
	void CGItemBreakRecv(PMSG_ITEM_REPAIR_RECV* lpMsg, int aIndex);
	void CGItemRepairRecv(PMSG_ITEM_REPAIR_RECV* lpMsg, int aIndex);
	void GCItemMoveSend(int aIndex, uint16_t result, uint16_t slotIndex, uint8_t* ItemInfo);
	void GCItemChangeSend(int aIndex, uint16_t slotIndex);
	void GCItemDeleteSend(int aIndex, uint16_t slotIndex, uint8_t flag);
	void GCItemDurSend(int aIndex, uint16_t slotIndex, uint8_t dur, uint8_t flag);
	void GCItemListSend(int aIndex, bool SendTime = false);
	void GCItemEquipmentSend(int aIndex);
	void GCItemModifySend(int aIndex, uint16_t slotIndex);
	void CGItemBuyConfirmRecv(PMSG_ITEM_BUY_NEW* lpMsg, int aIndex);
	void CGItemPostChatRecv(PMSG_ITEM_BUY_RECV* lpMsg, int aIndex);


	const char* GetItemName(int index);
private:
	type_map_item m_ItemInfo;
};

extern CItemManager gItemManager;
