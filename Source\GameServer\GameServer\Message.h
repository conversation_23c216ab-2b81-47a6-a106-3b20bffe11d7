// Message.h: interface for the CMessage class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

struct MESSAGE_INFO
{
	int32_t Index;
	char Message[128];
};


class LANGUAJE_INFO
{
public:
	LANGUAJE_INFO(int32_t Languaje, int32_t CodePage, int32_t NumByte);
	void Load();
	void OpenFile(std::string filename);
	char* GetText(int32_t index);
public:
	int32_t iLanguage;
	int32_t iCodePage;
	int32_t iNumByteForOneCharUTF8;
	std::unordered_map<int32_t, MESSAGE_INFO> Mensajes;
};

class CMessage
{
public:
	CMessage();
	virtual ~CMessage();
	void Load(char* path);
	char* GlobalText(int32_t index, int32_t Languaje = 0);
private:
	char m_DefaultMessage[128];
	std::map<int32_t, LANGUAJE_INFO> m_MessageInfo;
};

extern CMessage gMessage;
