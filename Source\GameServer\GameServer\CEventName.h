#pragma once
#define MAX_SIZE_EVENT_TEMPLATE			50

#define BLOOD_CASTLE_TIME				0
#define DEVIL_SQUARE_TIME				1
#define CHAOS_CASTLE_TIME				2
#define ILUSION_TEMP_TIME				3
#define ONLY_LOTTERY_TIME				4
#define QUIZ_EVENT_TIME					5
#define BONUS_EVENT_TIME				6
#define DROP_EVENT_TIME					7
#define KING_EVENT_TIME					8
#define TVT_EVENT_TIME					9
#define GVG_EVENT_TIME					10
#define MOSS_MERCH_TIME					11
//#define BLOOD_CASTLE_TIME	1


struct TEMPLATE_EVENT
{
	int m_Key;
	int m_RemainTime;
#ifdef GS_CUSTOM_DAT_MSG_ID
	int m_NameID;
#else
	std::string m_Name;
#endif // GS_CUSTOM_DAT_MSG_ID
	TEMPLATE_EVENT() : m_Key(-1), m_NameID(-1), m_RemainTime(-1)
	{
	}

	TEMPLATE_EVENT(int key, const int& NameID) : m_Key(key), m_NameID(NameID), m_RemainTime(-1)
	{
	}

	const int GetNameID()
	{
		return m_NameID;
	}
};


class CEventName
{
public:
	CEventName();
	virtual~CEventName();
	void Release();
	void OpenFile(std::string filename);
	void push_back(int switch_on, const TEMPLATE_EVENT& info);

	bool IsArena(int Index);
	bool IsGlobal(int Index);
	bool IsInvasion(int Index);


	int ArenaRemainTime(int Index);
	int GlobalRemainTime(int Index);
	int InvasionRemainTime(int Index);

	const int ArenaNameID(int Index);
	const int GlobalNameID(int Index);
	const int InvasionNameID(int Index);

	void ArenaRemainTime(int Index, int Time);
	void GlobalRemainTime(int Index, int Time);
	void InvasionRemainTime(int Index, int Time);
private:
	std::vector<TEMPLATE_EVENT> m_EventGlobal;
	std::vector<TEMPLATE_EVENT> m_InvasionGlobal;
	std::vector<TEMPLATE_EVENT> m_ArenaGlobal;
};

extern CEventName gEventName;