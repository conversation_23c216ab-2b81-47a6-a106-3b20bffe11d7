/**
 * Drop System Test Module
 * 掉落系统测试模块
 * 
 * This file provides testing functions for the monster drop system
 * 本文件提供怪物掉落系统的测试功能
 */

#include "stdafx.h"
#include "DropSystemTest.h"
#include "ItemDrop.h"
#include "AppointItemDrop.h"
#include "MonsterManager.h"
#include "Log.h"
#include "Util.h"

CDropSystemTest gDropSystemTest;

CDropSystemTest::CDropSystemTest()
{
    m_TestResults.clear();
}

CDropSystemTest::~CDropSystemTest()
{
    m_TestResults.clear();
}

/**
 * Initialize test environment
 * 初始化测试环境
 */
bool CDropSystemTest::Initialize()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Initializing test environment...");
    
    // Clear previous test results
    m_TestResults.clear();
    
    LogAdd(LOG_BLUE, "[DropSystemTest] Test environment initialized successfully");
    return true;
}

/**
 * Test ItemDrop configuration loading
 * 测试ItemDrop配置加载
 */
bool CDropSystemTest::TestItemDropLoading()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Testing ItemDrop configuration loading...");
    
    try
    {
        // Test loading ItemDrop.txt
        gItemDrop.Load(const_cast<char*>("Data\\Item\\ItemDrop.txt"));
        
        LogAdd(LOG_GREEN, "[DropSystemTest] ItemDrop configuration loaded successfully");
        m_TestResults["ItemDropLoading"] = true;
        return true;
    }
    catch (...)
    {
        LogAdd(LOG_RED, "[DropSystemTest] Failed to load ItemDrop configuration");
        m_TestResults["ItemDropLoading"] = false;
        return false;
    }
}

/**
 * Test AppointItemDrop configuration loading
 * 测试AppointItemDrop配置加载
 */
bool CDropSystemTest::TestAppointItemDropLoading()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Testing AppointItemDrop configuration loading...");
    
    try
    {
        // Test loading AppointItemDrop.xml
        bool result = g_AppointItemDrop.LoadAppointItemDropScript("Data\\Drop\\AppointItemDrop.xml");
        
        if (result)
        {
            LogAdd(LOG_GREEN, "[DropSystemTest] AppointItemDrop configuration loaded successfully");
            LogAdd(LOG_BLUE, "[DropSystemTest] Loaded %d drop groups", g_AppointItemDrop.GetGroupCount());
            m_TestResults["AppointItemDropLoading"] = true;
            return true;
        }
        else
        {
            LogAdd(LOG_RED, "[DropSystemTest] Failed to load AppointItemDrop configuration");
            m_TestResults["AppointItemDropLoading"] = false;
            return false;
        }
    }
    catch (...)
    {
        LogAdd(LOG_RED, "[DropSystemTest] Exception occurred while loading AppointItemDrop configuration");
        m_TestResults["AppointItemDropLoading"] = false;
        return false;
    }
}

/**
 * Test Monster 1000 configuration
 * 测试怪物1000配置
 */
bool CDropSystemTest::TestMonster1000Configuration()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Testing Monster 1000 configuration...");
    
    try
    {
        // Get monster 1000 information
        MONSTER_INFO* lpMonsterInfo = gMonsterManager.GetInfo(1000);
        
        if (lpMonsterInfo != nullptr)
        {
            LogAdd(LOG_GREEN, "[DropSystemTest] Monster 1000 found: %s", lpMonsterInfo->Name);
            LogAdd(LOG_BLUE, "[DropSystemTest] Level: %d, ItemRate: %d, MoneyRate: %d", 
                   lpMonsterInfo->Level, lpMonsterInfo->ItemRate, lpMonsterInfo->MoneyRate);
            m_TestResults["Monster1000Config"] = true;
            return true;
        }
        else
        {
            LogAdd(LOG_RED, "[DropSystemTest] Monster 1000 not found in configuration");
            m_TestResults["Monster1000Config"] = false;
            return false;
        }
    }
    catch (...)
    {
        LogAdd(LOG_RED, "[DropSystemTest] Exception occurred while testing Monster 1000 configuration");
        m_TestResults["Monster1000Config"] = false;
        return false;
    }
}

/**
 * Test drop rate calculation
 * 测试掉落概率计算
 */
bool CDropSystemTest::TestDropRateCalculation()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Testing drop rate calculation...");
    
    try
    {
        // Simulate drop rate calculation for different scenarios
        int baseDropRate = 50000; // 5% base drop rate
        int testCases = 1000;
        int successCount = 0;
        
        for (int i = 0; i < testCases; i++)
        {
            int randomValue = GetLargeRand() % 1000000;
            if (randomValue < baseDropRate)
            {
                successCount++;
            }
        }
        
        float actualRate = (float)successCount / testCases * 100.0f;
        float expectedRate = (float)baseDropRate / 10000.0f; // Convert to percentage
        float deviation = abs(actualRate - expectedRate);
        
        LogAdd(LOG_BLUE, "[DropSystemTest] Drop rate test results:");
        LogAdd(LOG_BLUE, "[DropSystemTest] Expected: %.2f%%, Actual: %.2f%%, Deviation: %.2f%%", 
               expectedRate, actualRate, deviation);
        
        // Allow 2% deviation for randomness
        if (deviation <= 2.0f)
        {
            LogAdd(LOG_GREEN, "[DropSystemTest] Drop rate calculation test passed");
            m_TestResults["DropRateCalculation"] = true;
            return true;
        }
        else
        {
            LogAdd(LOG_RED, "[DropSystemTest] Drop rate calculation test failed - deviation too high");
            m_TestResults["DropRateCalculation"] = false;
            return false;
        }
    }
    catch (...)
    {
        LogAdd(LOG_RED, "[DropSystemTest] Exception occurred during drop rate calculation test");
        m_TestResults["DropRateCalculation"] = false;
        return false;
    }
}

/**
 * Test item creation
 * 测试物品创建
 */
bool CDropSystemTest::TestItemCreation()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Testing item creation...");
    
    try
    {
        // Test creating various items
        WORD testItems[] = {
            GET_ITEM(0, 0),   // Short Sword
            GET_ITEM(1, 0),   // Hand Axe
            GET_ITEM(4, 0),   // Leather Helm
            GET_ITEM(12, 30), // Jewel of Bless
            GET_ITEM(14, 0)   // Apple
        };
        
        int itemCount = sizeof(testItems) / sizeof(WORD);
        int successCount = 0;
        
        for (int i = 0; i < itemCount; i++)
        {
            if (gItemManager.CheckItemMake(testItems[i]) != 0)
            {
                successCount++;
                LogAdd(LOG_BLUE, "[DropSystemTest] Item %d creation test passed", testItems[i]);
            }
            else
            {
                LogAdd(LOG_RED, "[DropSystemTest] Item %d creation test failed", testItems[i]);
            }
        }
        
        if (successCount == itemCount)
        {
            LogAdd(LOG_GREEN, "[DropSystemTest] All item creation tests passed");
            m_TestResults["ItemCreation"] = true;
            return true;
        }
        else
        {
            LogAdd(LOG_RED, "[DropSystemTest] %d/%d item creation tests failed", 
                   itemCount - successCount, itemCount);
            m_TestResults["ItemCreation"] = false;
            return false;
        }
    }
    catch (...)
    {
        LogAdd(LOG_RED, "[DropSystemTest] Exception occurred during item creation test");
        m_TestResults["ItemCreation"] = false;
        return false;
    }
}

/**
 * Run all tests
 * 运行所有测试
 */
bool CDropSystemTest::RunAllTests()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] Starting comprehensive drop system tests...");
    
    if (!Initialize())
    {
        LogAdd(LOG_RED, "[DropSystemTest] Failed to initialize test environment");
        return false;
    }
    
    bool allTestsPassed = true;
    
    // Run individual tests
    allTestsPassed &= TestItemDropLoading();
    allTestsPassed &= TestAppointItemDropLoading();
    allTestsPassed &= TestMonster1000Configuration();
    allTestsPassed &= TestDropRateCalculation();
    allTestsPassed &= TestItemCreation();
    
    // Generate test report
    GenerateTestReport();
    
    if (allTestsPassed)
    {
        LogAdd(LOG_GREEN, "[DropSystemTest] All tests passed successfully!");
    }
    else
    {
        LogAdd(LOG_RED, "[DropSystemTest] Some tests failed. Check the test report for details.");
    }
    
    return allTestsPassed;
}

/**
 * Generate test report
 * 生成测试报告
 */
void CDropSystemTest::GenerateTestReport()
{
    LogAdd(LOG_BLUE, "[DropSystemTest] ========== TEST REPORT ==========");
    
    int passedTests = 0;
    int totalTests = m_TestResults.size();
    
    for (auto& result : m_TestResults)
    {
        const char* status = result.second ? "PASSED" : "FAILED";
        LogAdd(result.second ? LOG_GREEN : LOG_RED, 
               "[DropSystemTest] %s: %s", result.first.c_str(), status);
        
        if (result.second)
        {
            passedTests++;
        }
    }
    
    LogAdd(LOG_BLUE, "[DropSystemTest] ================================");
    LogAdd(LOG_BLUE, "[DropSystemTest] Total Tests: %d", totalTests);
    LogAdd(LOG_BLUE, "[DropSystemTest] Passed: %d", passedTests);
    LogAdd(LOG_BLUE, "[DropSystemTest] Failed: %d", totalTests - passedTests);
    LogAdd(LOG_BLUE, "[DropSystemTest] Success Rate: %.1f%%", 
           totalTests > 0 ? (float)passedTests / totalTests * 100.0f : 0.0f);
    LogAdd(LOG_BLUE, "[DropSystemTest] ================================");
}

/**
 * Get test result for specific test
 * 获取特定测试的结果
 */
bool CDropSystemTest::GetTestResult(const std::string& testName)
{
    auto it = m_TestResults.find(testName);
    return (it != m_TestResults.end()) ? it->second : false;
}
