// EffectManager.h: interface for the CEffectManager class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "Effect.h"
#include "Protocol.h"
#include "User.h"

#define MAX_EFFECT 256
#if(GAMESERVER_UPDATE>=701)
#define MAX_EFFECT_LIST 32
#else
#define MAX_EFFECT_LIST 16
#endif

#define EFFECT_RANGE(x) (((x)<0)?0:((x)>=MAX_EFFECT)?0:1)

enum eEffectNumber
{
	EFFECT_GREATER_DAMAGE = 1,
	EFFECT_GREATER_DEFENSE = 2,
	EFFECT_ELF_BUFFER = 3,
	EFFECT_MANA_SHIELD = 4,
	EFFECT_GREATER_CRITICAL_DAMAGE = 5,
	EFFECT_INFINITY_ARROW = 6,
	EFFECT_BP_RECOVERY = 7,
	EFFECT_GREATER_LIFE = 8,
	EFFECT_GREATER_MANA = 9,
	EFFECT_BLESS_POTION = 10,
	EFFECT_SOUL_POTION = 11,
	EFFECT_DISABLE_MAGIC = 12,
	EFFECT_CASTLE_GATE_STATE = 13,
	EFFECT_GUILD_STATE1 = 14,
	EFFECT_GUILD_STATE2 = 15,
	EFFECT_GUILD_STATE3 = 16,
	EFFECT_GUILD_STATE4 = 17,
	EFFECT_INVISIBILITY = 18,
	EFFECT_GUILD_STATE5 = 19,
	EFFECT_CASTLE_CROWN_STATE = 20,
	EFFECT_CRYWOLF_STATE1 = 21,
	EFFECT_CRYWOLF_STATE2 = 22,
	EFFECT_CRYWOLF_STATE3 = 23,
	EFFECT_CRYWOLF_STATE4 = 24,
	EFFECT_CRYWOLF_STATE5 = 25,
	EFFECT_CRYWOLF_STATE6 = 26,
	EFFECT_CRYWOLF_STATE7 = 27,
	EFFECT_GAME_MASTER = 28,
	EFFECT_SEAL_OF_ASCENSION1 = 29,
	EFFECT_SEAL_OF_WEALTH1 = 30,
	EFFECT_SEAL_OF_SUSTENANCE1 = 31,
	EFFECT_ORDER_OF_SPEED = 32,
	EFFECT_ORDER_OF_SUBLIMATION = 33,
	EFFECT_ORDER_OF_PROTECTION = 34,
	EFFECT_HALLOWEEN1 = 35,
	EFFECT_HALLOWEEN2 = 36,
	EFFECT_HALLOWEEN3 = 37,
	EFFECT_HALLOWEEN4 = 38,
	EFFECT_HALLOWEEN5 = 39,
	EFFECT_SEAL_OF_ASCENSION2 = 40,
	EFFECT_SEAL_OF_WEALTH2 = 41,
	EFFECT_SEAL_OF_SUSTENANCE2 = 42,
	EFFECT_SEAL_OF_MOVEMENT = 43,
	EFFECT_SCROLL_OF_QUICK = 44,
	EFFECT_SCROLL_OF_DEFENSE = 45,
	EFFECT_SCROLL_OF_DAMAGE = 46,
	EFFECT_SCROLL_OF_MAGIC_DAMAGE = 47,
	EFFECT_SCROLL_OF_LIFE = 48,
	EFFECT_SCROLL_OF_MANA = 49,
	EFFECT_ELIXIR_OF_STRENGTH = 50,
	EFFECT_ELIXIR_OF_DEXTERITY = 51,
	EFFECT_ELIXIR_OF_VITALITY = 52,
	EFFECT_ELIXIR_OF_ENERGY = 53,
	EFFECT_ELIXIR_OF_LEADERSHIP = 54,
	EFFECT_POISON = 55,
	EFFECT_ICE = 56,
	EFFECT_ICE_ARROW = 57,
	EFFECT_FIRE_SLASH = 58,
	EFFECT_PHYSI_DAMAGE_IMMUNITY = 59,
	EFFECT_MAGIC_DAMAGE_IMMUNITY = 60,
	EFFECT_STERN = 61,
	EFFECT_MAGIC_DEFENSE = 62,
	EFFECT_MONSTER_PHYSI_DAMAGE_IMMUNITY = 63,
	EFFECT_MONSTER_MAGIC_DAMAGE_IMMUNITY = 64,
	EFFECT_ORDER_OF_RESTRAINT = 65,
	EFFECT_CRYWOLF_STATE8 = 66,
	EFFECT_CRYWOLF_STATE9 = 67,
	EFFECT_CRYWOLF_STATE10 = 68,
	EFFECT_CRYWOLF_STATE11 = 69,
	EFFECT_CRYWOLF_STATE12 = 70,
	EFFECT_DAMAGE_REFLECT = 71,
	EFFECT_SLEEP = 72,
	EFFECT_BLIND = 73,
	EFFECT_NEIL = 74,
	EFFECT_SAHAMUTT = 75,
	EFFECT_LESSER_DAMAGE = 76,
	EFFECT_LESSER_DEFENSE = 77,
	EFFECT_CHERRY_BLOSSOM1 = 78,
	EFFECT_CHERRY_BLOSSOM2 = 79,
	EFFECT_CHERRY_BLOSSOM3 = 80,
	EFFECT_SWORD_POWER = 81,
	EFFECT_MAGIC_CIRCLE = 82,
	EFFECT_SWORD_SLASH = 83,
	EFFECT_LIGHTNING_STORM = 84,
	EFFECT_RED_STORM = 85,
	EFFECT_FROZEN_STAB = 86,
	EFFECT_SEAL_OF_LIFE = 87,
	EFFECT_SEAL_OF_MANA = 88,
	EFFECT_SCROLL_OF_BATTLE = 89,
	EFFECT_SCROLL_OF_STRENGTH = 90,
	EFFECT_CHRISTMAS1 = 91,
	EFFECT_CHRISTMAS2 = 92,
	EFFECT_CHRISTMAS3 = 93,
	EFFECT_CHRISTMAS4 = 94,
	EFFECT_CHRISTMAS5 = 95,
	EFFECT_CHRISTMAS6 = 96,
	EFFECT_CHRISTMAS7 = 97,
	EFFECT_DUEL_ARENA_WATCH = 98,
	EFFECT_TALISMAN_OF_GUARDIAN = 99,
	EFFECT_TALISMAN_OF_PROTECTION = 100,
	EFFECT_MASTER_SEAL_OF_ASCENSION = 101,
	EFFECT_MASTER_SEAL_OF_WEALTH = 102,
	EFFECT_GLADIATORS_GLORY = 103,
	EFFECT_DOUBLE_GOER_DELETE = 105,
	EFFECT_PARTY_EXPERIENCE_BONUS = 112,
	EFFECT_MAX_AG_BOOST_AURA = 113,
	EFFECT_MAX_SD_BOOST_AURA = 114,
	EFFECT_SCROLL_OF_HEALING = 121,
	EFFECT_HAWK_FIGURINE = 122,
	EFFECT_GOAT_FIGURINE = 123,
	EFFECT_OAK_CHARM = 124,
	EFFECT_MAPLE_CHARM = 125,
	EFFECT_GOLDEN_OAK_CHARM = 126,
	EFFECT_GOLDEN_MAPLE_CHARM = 127,
	EFFECT_WORN_HORSESHOE = 128,
	EFFECT_GREATER_IGNORE_DEFENSE_RATE = 129,
	EFFECT_FITNESS = 130,
	EFFECT_GREATER_DEFENSE_SUCCESS_RATE = 131,
	EFFECT_IRON_DEFENSE = 134,
	EFFECT_GREATER_LIFE_ENHANCED = 135,
	EFFECT_GREATER_LIFE_MASTERED = 136,
	EFFECT_DEATH_STAB_ENHANCED = 137,
	EFFECT_MAGIC_CIRCLE_IMPROVED = 138,
	EFFECT_MAGIC_CIRCLE_ENHANCED = 139,
	EFFECT_MANA_SHIELD_MASTERED = 140,
	EFFECT_FROZEN_STAB_MASTERED = 141,
	EFFECT_BLESS = 142,
	EFFECT_INFINITY_ARROW_IMPROVED = 143,
	EFFECT_BLIND_IMPROVED = 144,
	EFFECT_DRAIN_LIFE_ENHANCED = 145,
	EFFECT_ICE_STORM_ENHANCED = 146,
	EFFECT_EARTH_PRISON = 147,
	EFFECT_GREATER_CRITICAL_DAMAGE_MASTERED = 148,
	EFFECT_GREATER_CRITICAL_DAMAGE_EXTENDED = 149,
	EFFECT_SWORD_POWER_IMPROVED = 150,
	EFFECT_SWORD_POWER_ENHANCED = 151,
	EFFECT_SWORD_POWER_MASTERED = 152,
	EFFECT_GREATER_DEFENSE_SUCCESS_RATE_IMPROVED = 153,
	EFFECT_GREATER_DEFENSE_SUCCESS_RATE_ENHANCED = 154,
	EFFECT_FITNESS_IMPROVED = 155,
	EFFECT_DRAGON_ROAR_ENHANCED = 157,
	EFFECT_CHAIN_DRIVER_ENHANCED = 158,
	EFFECT_POISON_ARROW = 159,
	EFFECT_POISON_ARROW_IMPROVED = 160,
	EFFECT_BLESS_IMPROVED = 161,
	EFFECT_LESSER_DAMAGE_IMPROVED = 162,
	EFFECT_LESSER_DEFENSE_IMPROVED = 163,
	EFFECT_FIRE_SLASH_ENHANCED = 164,
	EFFECT_IRON_DEFENSE_IMPROVED = 165,
	EFFECT_BLOOD_HOWLING = 166,
	EFFECT_BLOOD_HOWLING_IMPROVED = 167,
	EFFECT_PENTAGRAM_JEWEL_HALF_SD = 174,
	EFFECT_PENTAGRAM_JEWEL_HALF_MP = 175,
	EFFECT_PENTAGRAM_JEWEL_HALF_SPEED = 176,
	EFFECT_PENTAGRAM_JEWEL_HALF_HP = 177,
	EFFECT_PENTAGRAM_JEWEL_STUN = 178,
	EFFECT_PENTAGRAM_JEWEL_SLOW = 186,
	EFFECT_TALISMAN_OF_ASCENSION1 = 190,
	EFFECT_TALISMAN_OF_ASCENSION2 = 191,
	EFFECT_TALISMAN_OF_ASCENSION3 = 192,
	EFFECT_SEAL_OF_ASCENSION3 = 193,
	EFFECT_MASTER_SEAL_OF_ASCENSION2 = 194,
	EFFECT_BLESSING_OF_LIGHT = 195,
	EFFECT_MASTER_SCROLL_OF_DEFENSE = 196,
	EFFECT_MASTER_SCROLL_OF_MAGIC_DAMAGE = 197,
	EFFECT_MASTER_SCROLL_OF_LIFE = 198,
	EFFECT_MASTER_SCROLL_OF_MANA = 199,
	EFFECT_MASTER_SCROLL_OF_DAMAGE = 200,
	EFFECT_MASTER_SCROLL_OF_HEALING = 201,
	EFFECT_MASTER_SCROLL_OF_BATTLE = 202,
	EFFECT_MASTER_SCROLL_OF_STRENGTH = 203,
	EFFECT_MASTER_SCROLL_OF_QUICK = 204,
};

//**********************************************//
//************ GameServer -> Client ************//
//**********************************************//

struct PMSG_EFFECT_STATE_SEND
{
	PBMSG_HEAD header; // C1:07
	BYTE state;
	BYTE index[2];
	BYTE effect;
};

struct PMSG_PERIODIC_EFFECT_SEND
{
	PBMSG_HEAD header; // C1:2D
	WORD group;
	WORD value;
	BYTE state;
	DWORD time;
	BYTE effect;
};

struct PMSG_PARTY_EFFECT_LIST_SEND
{
	PBMSG_HEAD header; // C1:2E
	char name[11];
	BYTE count;
};

struct PMSG_PARTY_EFFECT_LIST
{
	#pragma pack(1)
	BYTE effect;
	DWORD count;
	#pragma pack()
};

//**********************************************//
//**********************************************//
//**********************************************//

struct EFFECT_INFO
{
	int Index;
	int Group;
	int ItemIndex;
	char Name[32];
	int Save;
	int Type;
	int Flag;
	int Count;
	int Value[4];
};

class CEffectManager
{
public:
	CEffectManager();
	virtual ~CEffectManager();
	void Init();
	void Load(char* path);
	void SetInfo(EFFECT_INFO info);
	EFFECT_INFO* GetInfo(int index);
	EFFECT_INFO* GetInfoByItem(int ItemIndex);
	void MainProc();
	bool AddEffect(LPOBJ lpObj,bool type,int index,int count,WORD value1,WORD value2,WORD value3,WORD value4);
	bool DelEffect(LPOBJ lpObj,int index);
	bool DelEffectByGroup(LPOBJ lpObj,int group);
	CEffect* GetEffect(LPOBJ lpObj,int index);
	CEffect* GetEffectByGroup(LPOBJ lpObj,int group);
	bool CheckEffect(LPOBJ lpObj,int index);
	bool CheckEffectByGroup(LPOBJ lpObj,int group);
	void InsertEffect(LPOBJ lpObj,CEffect* lpEffect);
	void RemoveEffect(LPOBJ lpObj,CEffect* lpEffect);
	bool ConvertEffectByte(CEffect* lpEffect,BYTE* lpMsg);
	void EffectByteConvert(BYTE* lpMsg,CEffect* lpEffect);
	int GetActiveBuffCount(LPOBJ lpObj,BYTE* lpMsg,int* size);
	int GeneratePartyEffectList(LPOBJ lpObj,BYTE* lpMsg,int* size);
	bool CheckStunEffect(LPOBJ lpObj);
	bool CheckImmobilizeEffect(LPOBJ lpObj);
	void ClearAllEffect(LPOBJ lpObj);
	void ClearDebuffEffect(LPOBJ lpObj,int count);
	void PeriodicEffect(LPOBJ lpObj,CEffect* lpEffect);
	void GCEffectStateSend(LPOBJ lpObj,BYTE state,BYTE effect);
	void GCPeriodicEffectSend(LPOBJ lpObj,WORD group,WORD value,BYTE state,DWORD time,BYTE effect);
	void GCPartyEffectListSend(LPOBJ lpObj);
private:
	EFFECT_INFO m_EffectInfo[MAX_EFFECT];
};

extern CEffectManager gEffectManager;
