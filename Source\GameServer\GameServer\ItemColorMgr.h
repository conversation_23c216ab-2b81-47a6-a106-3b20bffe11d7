#pragma once
#include "User.h"

#if COLOR_ITEM_DEF

#define MAX_TABLE_COLOR				16
#define	ARGB(a,r,g,b)				(((uint32_t)(a))<<24 | (uint32_t)(r) | ((uint32_t)(g))<<8 | ((uint32_t)(b))<<16)


struct MAKE_INFO_COLOR
{
	uint16_t ItemType;
	uint16_t iColor;
};

class CItemColorMgr
{
public:
	CItemColorMgr();
	~CItemColorMgr();

	void Release();
	void LoadFiles(char* path);

	uint32_t GetColorById(int id);
	MAKE_INFO_COLOR* GetColorOption(int ItemIndex);
	bool CharacterUseDrawColor(LPOBJ lpObj, int SourceSlot, int TargetSlot);
private:
	uint32_t iTableColor[MAX_TABLE_COLOR];
	std::vector<MAKE_INFO_COLOR> m_ItemColorInfo;
};

extern CItemColorMgr gItemColorMgr;

#endif // COLOR_ITEM_DEF

