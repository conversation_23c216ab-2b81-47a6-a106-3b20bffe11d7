// Message.cpp: implementation of the CMessage class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "Message.h"
#include "MemScript.h"
#include "Util.h"
#include "Path.h"

CMessage gMessage;



LANGUAJE_INFO::LANGUAJE_INFO(int32_t Languaje, int32_t CodePage, int32_t NumByte)
{
	this->iLanguage = Languaje;
	this->iCodePage = CodePage;
	this->iNumByteForOneCharUTF8 = NumByte;

	this->Load();
}

void LANGUAJE_INFO::Load()
{
	std::string filename;

	char name[100];
	sprintf_s(name, "%03d - Message.txt", this->iLanguage);


	filename = gPath.GetFullPath("Lang\\");

	filename += name;

	this->OpenFile(filename);
}

void LANGUAJE_INFO::OpenFile(std::string filename)
{
	CMemScript* lpMemScript = new CMemScript;

	if (lpMemScript == 0)
	{
		ErrorMessageBox(MEM_SCRIPT_ALLOC_ERROR, filename.c_str());
		return;
	}

	if (lpMemScript->SetBuffer(filename.c_str()) == 0)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
		delete lpMemScript;
		return;
	}

	this->Mensajes.clear();

	try
	{
		while (true)
		{
			if (lpMemScript->GetToken() == TOKEN_END)
			{
				break;
			}

			if (strcmp("end", lpMemScript->GetString()) == 0)
			{
				break;
			}

			MESSAGE_INFO msg;

			int32_t index = lpMemScript->GetNumber();

			msg.Index = index;

			strcpy_s(msg.Message, lpMemScript->GetAsString());

			this->Mensajes[index] = msg;
		}
	}
	catch (...)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
	}

	delete lpMemScript;
}

char* LANGUAJE_INFO::GetText(int32_t index)
{
	auto it = Mensajes.find(index);

	if (it != Mensajes.end())
	{
		return it->second.Message;
	}
	else
	{
		static char const_text[MAX_PATH];
		sprintf_s(const_text, "Could not find message %d!", index);
	}

	return 0;
}



//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMessage::CMessage() // OK
{
	memset(this->m_DefaultMessage,0,sizeof(this->m_DefaultMessage));

	this->m_MessageInfo.clear();
}

CMessage::~CMessage() // OK
{
	this->m_MessageInfo.clear();
}

void CMessage::Load(char* path) // OK
{
	CMemScript* lpMemScript = new CMemScript;

	if(lpMemScript == 0)
	{
		ErrorMessageBox(MEM_SCRIPT_ALLOC_ERROR,path);
		return;
	}

	if(lpMemScript->SetBuffer(path) == 0)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
		delete lpMemScript;
		return;
	}

	this->m_MessageInfo.clear();

	try
	{
		while(true)
		{
			if(lpMemScript->GetToken() == TOKEN_END)
			{
				break;
			}

			if(strcmp("end",lpMemScript->GetString()) == 0)
			{
				break;
			}

			int32_t Languaje = lpMemScript->GetNumber();

			int32_t CodePage = lpMemScript->GetAsNumber();

			int32_t iNumByteForOneCharUTF8 = lpMemScript->GetAsNumber();

			lpMemScript->GetAsString();

			LANGUAJE_INFO msg(Languaje, CodePage, iNumByteForOneCharUTF8);

			this->m_MessageInfo.insert(std::pair<int32_t, LANGUAJE_INFO>(Languaje, msg));
		}
	}
	catch(...)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
	}

	delete lpMemScript;
}

char* CMessage::GlobalText(int32_t index, int32_t Languaje) // OK
{
	std::map<int32_t, LANGUAJE_INFO>::iterator it = this->m_MessageInfo.find(Languaje);

	if(it != this->m_MessageInfo.end())
	{
		LANGUAJE_INFO* msg = &it->second;

		return msg->GetText(index);
	}

	wsprintf(this->m_DefaultMessage, "Could not find message %d!", index);

	return this->m_DefaultMessage;
}