// MonsterDropConfig.cpp: implementation of the CMonsterDropConfig class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "MonsterDropConfig.h"
#include "MemScript.h"
#include "Log.h"
#include "ItemManager.h"
#include "ItemOptionRate.h"
#include "BonusManager.h"
#include "MapManager.h"
#include "ServerInfo.h"
#include "Monster.h"
#include "Util.h"

CMonsterDropConfig gMonsterDropConfig;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMonsterDropConfig::CMonsterDropConfig() {
    m_MonsterConfigs.clear();
    m_bInitialized = false;
}

CMonsterDropConfig::~CMonsterDropConfig() {
    Clear();
}

void CMonsterDropConfig::Clear() {
    m_MonsterConfigs.clear();
    m_bInitialized = false;
}

bool CMonsterDropConfig::LoadConfig(int monsterId, const char* filePath) {
    if (filePath == nullptr || strlen(filePath) == 0) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Invalid file path");
        return false;
    }
    
    MONSTER_DROP_CONFIG config;
    config.monsterId = monsterId;
    
    if (!ParseConfigFile(filePath, config)) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to parse config file: %s", filePath);
        return false;
    }
    
    // Store configuration
    m_MonsterConfigs[monsterId] = config;
    m_bInitialized = true;
    
    LogAdd(LOG_GREEN, "[MonsterDropConfig] Loaded configuration for monster %d from %s", 
           monsterId, filePath);
    LogAdd(LOG_BLUE, "[MonsterDropConfig] - Level groups: %d, Drop items: %d", 
           config.levelGroups.size(), config.dropItems.size());
    
    return true;
}

bool CMonsterDropConfig::ParseConfigFile(const char* filePath, MONSTER_DROP_CONFIG& config) {
    CMemScript* lpMemScript = new CMemScript;
    
    if (lpMemScript == nullptr) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Memory allocation failed");
        return false;
    }
    
    if (lpMemScript->SetBuffer(filePath) == 0) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to load file: %s", filePath);
        delete lpMemScript;
        return false;
    }
    
    bool result = true;
    
    try {
        int currentSection = 0;
        
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                continue;
            }
            
            // Read section number
            currentSection = lpMemScript->GetNumber();
            
            switch (currentSection) {
                case 0:
                    if (!ParseSection0(lpMemScript, config)) {
                        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to parse section 0");
                        result = false;
                        goto CLEANUP;
                    }
                    break;
                case 1:
                    if (!ParseSection1(lpMemScript, config)) {
                        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to parse section 1");
                        result = false;
                        goto CLEANUP;
                    }
                    break;
                case 2:
                    if (!ParseSection2(lpMemScript, config)) {
                        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to parse section 2");
                        result = false;
                        goto CLEANUP;
                    }
                    break;
                case 3:
                    if (!ParseSection3(lpMemScript, config)) {
                        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to parse section 3");
                        result = false;
                        goto CLEANUP;
                    }
                    break;
                default:
                    LogAdd(LOG_YELLOW, "[MonsterDropConfig] Unknown section: %d", currentSection);
                    break;
            }
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Exception during parsing: %s", lpMemScript->GetLastError());
        result = false;
    }
    
CLEANUP:
    delete lpMemScript;
    return result;
}

bool CMonsterDropConfig::ParseSection0(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config) {
    try {
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                break;
            }
            
            // Read description
            std::string description = lpMemScript->GetString();
            config.description = description;
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Error parsing section 0");
        return false;
    }
    
    return true;
}

bool CMonsterDropConfig::ParseSection1(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config) {
    try {
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                break;
            }
            
            // Read base configuration
            config.baseInfo.monsterLevel = lpMemScript->GetNumber();
            config.baseInfo.useFixedDrop = lpMemScript->GetAsNumber();
            config.baseInfo.fixedDropCount = lpMemScript->GetAsNumber();
            config.baseInfo.dropLevel = lpMemScript->GetAsNumber();
            config.baseInfo.dropRange = lpMemScript->GetAsNumber();
            config.baseInfo.reserved1 = lpMemScript->GetAsNumber();
            config.baseInfo.reserved2 = lpMemScript->GetAsNumber();
            config.baseInfo.reserved3 = lpMemScript->GetAsNumber();
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Error parsing section 1");
        return false;
    }
    
    return true;
}

bool CMonsterDropConfig::ParseSection2(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config) {
    try {
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                break;
            }
            
            // Skip comment lines
            std::string line = lpMemScript->GetString();
            if (line.find("//") == 0) {
                continue;
            }
            
            // Parse as number if it's not a comment
            int firstValue = 0;
            if (sscanf(line.c_str(), "%d", &firstValue) != 1) {
                continue; // Skip non-numeric lines
            }
            
            MONSTER_DROP_LEVEL_GROUP group;
            group.useLevel = firstValue;
            group.dropRate = lpMemScript->GetAsNumber();
            group.additionalMethod = lpMemScript->GetAsNumber();
            group.excellentOption = lpMemScript->GetAsNumber();
            group.ancientOption = lpMemScript->GetAsNumber();
            group.setOption = lpMemScript->GetAsNumber();
            group.socketOption = lpMemScript->GetAsNumber();
            group.minLevel = lpMemScript->GetAsNumber();
            group.maxLevel = lpMemScript->GetAsNumber();
            group.reserved1 = lpMemScript->GetAsNumber();
            group.reserved2 = lpMemScript->GetAsNumber();
            
            config.levelGroups.push_back(group);
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Error parsing section 2");
        return false;
    }
    
    return true;
}

bool CMonsterDropConfig::ParseSection3(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config) {
    try {
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                break;
            }
            
            // Skip comment lines
            std::string line = lpMemScript->GetString();
            if (line.find("//") == 0) {
                continue;
            }
            
            // Parse as number if it's not a comment
            int firstValue = 0;
            if (sscanf(line.c_str(), "%d", &firstValue) != 1) {
                continue; // Skip non-numeric lines
            }
            
            MONSTER_DROP_ITEM_INFO item;
            item.level = firstValue;
            item.itemType1 = lpMemScript->GetAsNumber();
            item.itemType2 = lpMemScript->GetAsNumber();
            item.minLevel = lpMemScript->GetAsNumber();
            item.maxLevel = lpMemScript->GetAsNumber();
            item.skillRate = lpMemScript->GetAsNumber();
            item.luckRate = lpMemScript->GetAsNumber();
            item.addOption = lpMemScript->GetAsNumber();
            item.quality = lpMemScript->GetAsNumber();
            item.excellentOption = lpMemScript->GetAsNumber();
            item.setItemValue = lpMemScript->GetAsNumber();
            item.socketOption = lpMemScript->GetAsNumber();
            item.duration = lpMemScript->GetAsNumber();
            item.dropCondition = lpMemScript->GetAsNumber();
            item.reserved = lpMemScript->GetAsNumber();
            item.dropRate = lpMemScript->GetAsNumber();
            
            config.dropItems.push_back(item);
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Error parsing section 3");
        return false;
    }
    
    return true;
}

bool CMonsterDropConfig::ProcessMonsterDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj) {
    if (!m_bInitialized || !OBJECT_RANGE(lpMonsterObj->Index) || !OBJECT_RANGE(lpTargetObj->Index)) {
        return false;
    }
    
    auto configIt = m_MonsterConfigs.find(lpMonsterObj->Class);
    if (configIt == m_MonsterConfigs.end()) {
        return false; // This monster has no configuration
    }
    
    MONSTER_DROP_CONFIG& config = configIt->second;
    
    // Process drop based on configuration type
    if (config.baseInfo.useFixedDrop == 1) {
        // Fixed drop mode
        return ProcessFixedDrop(lpMonsterObj, lpTargetObj, config);
    } else {
        // Random drop mode
        return ProcessRandomDrop(lpMonsterObj, lpTargetObj, config);
    }
}

bool CMonsterDropConfig::ProcessFixedDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj, const MONSTER_DROP_CONFIG& config) {
    int dropCount = 0;
    int maxDropCount = config.baseInfo.fixedDropCount;
    
    if (maxDropCount <= 0) {
        maxDropCount = 1; // Default to 1 if not specified
    }
    
    // Process all configured drop items until we reach the fixed count
    for (const auto& itemInfo : config.dropItems) {
        if (dropCount >= maxDropCount) {
            break;
        }
        
        // Check drop conditions
        if (!CheckDropCondition(lpTargetObj, itemInfo.dropCondition)) {
            continue;
        }
        
        // Check level requirements
        if (itemInfo.level >= 0 && !CheckLevelRequirement(lpTargetObj, itemInfo)) {
            continue;
        }
        
        // Calculate final drop rate
        int finalDropRate = CalculateDropRate(itemInfo.dropRate, lpTargetObj, lpMonsterObj);
        
        // Drop determination
        if (GetLargeRand() % 10000 < finalDropRate) {
            if (CreateDropItem(lpTargetObj, itemInfo)) {
                dropCount++;
            }
        }
    }
    
    return dropCount > 0;
}

bool CMonsterDropConfig::ProcessRandomDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj, const MONSTER_DROP_CONFIG& config) {
    bool hasDropped = false;
    
    // Process all configured drop items
    for (const auto& itemInfo : config.dropItems) {
        // Check drop conditions
        if (!CheckDropCondition(lpTargetObj, itemInfo.dropCondition)) {
            continue;
        }
        
        // Check level requirements
        if (itemInfo.level >= 0 && !CheckLevelRequirement(lpTargetObj, itemInfo)) {
            continue;
        }
        
        // Calculate final drop rate
        int finalDropRate = CalculateDropRate(itemInfo.dropRate, lpTargetObj, lpMonsterObj);
        
        // Drop determination
        if (GetLargeRand() % 10000 < finalDropRate) {
            if (CreateDropItem(lpTargetObj, itemInfo)) {
                hasDropped = true;
            }
        }
    }
    
    return hasDropped;
}

int CMonsterDropConfig::CalculateDropRate(int baseRate, LPOBJ lpTargetObj, LPOBJ lpMonsterObj) {
    if (baseRate <= 0) {
        return 0;
    }
    
    int finalRate = baseRate;
    
    // Apply item drop rate bonuses
    int itemDropRate = gServerInfo.m_ItemDropRate[lpTargetObj->AccountLevel];
    itemDropRate = (itemDropRate * (lpTargetObj->ItemDropRate + lpTargetObj->EffectOption.AddItemDropRate)) / 100;
    itemDropRate = (itemDropRate * gMapManager.GetMapItemDropRate(lpMonsterObj->Map)) / 100;
    itemDropRate = (itemDropRate * gBonusManager.GetBonusValue(lpTargetObj, BONUS_INDEX_ITEM_DROP_RATE, 100, -1, -1, lpMonsterObj->Class, lpMonsterObj->Level)) / 100;
    
    finalRate = (finalRate * itemDropRate) / 100;
    
    // Ensure rate doesn't exceed maximum
    finalRate = min(finalRate, 10000);
    
    return finalRate;
}

bool CMonsterDropConfig::CreateDropItem(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo) {
    CItem item;
    
    // Set basic item information
    item.m_Index = GET_ITEM(itemInfo.itemType1, itemInfo.itemType2);
    
    // Validate item
    ITEM_INFO* itemInfoPtr = gItemManager.GetInfo(item.m_Index);
    if (itemInfoPtr == nullptr) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Invalid item: %d,%d", 
               itemInfo.itemType1, itemInfo.itemType2);
        return false;
    }
    
    // Set item level
    int itemLevel = itemInfo.minLevel;
    if (itemInfo.maxLevel > itemInfo.minLevel) {
        itemLevel += GetLargeRand() % (itemInfo.maxLevel - itemInfo.minLevel + 1);
    }
    item.m_Level = min(itemLevel, 15);
    
    // Set skill option
    if (itemInfo.skillRate > 0 && (GetLargeRand() % 100) < itemInfo.skillRate) {
        item.m_Option1 = 1;
    }
    
    // Set luck option
    if (itemInfo.luckRate > 0 && (GetLargeRand() % 100) < itemInfo.luckRate) {
        item.m_Option2 = 1;
    }
    
    // Set additional option
    if (itemInfo.addOption > 0) {
        if (itemInfo.addOption <= 7) {
            // Random additional 0~(addOption*4)
            item.m_Option3 = GetLargeRand() % (itemInfo.addOption * 4 + 1);
        } else if (itemInfo.addOption == 8) {
            // Fixed additional 28
            item.m_Option3 = 28;
        } else if (itemInfo.addOption >= 11 && itemInfo.addOption <= 17) {
            // Fixed additional 4~28
            item.m_Option3 = 4 + (itemInfo.addOption - 11) * 4;
        }
    }
    
    // Set quality
    if (itemInfo.quality > 0 && itemInfo.quality <= 255) {
        item.m_JewelOfHarmonyOption = itemInfo.quality;
    }
    
    // Set excellent option
    if (itemInfo.excellentOption > 0) {
        if (itemInfo.excellentOption <= 6) {
            // Random 1~6 excellent options
            int excellentCount = 1 + GetLargeRand() % itemInfo.excellentOption;
            item.m_NewOption = gItemOptionRate.GetRandomItemOption(excellentCount);
        } else if (itemInfo.excellentOption == 7) {
            // Full excellent
            item.m_NewOption = 63; // 111111 in binary
        } else if (itemInfo.excellentOption >= 101 && itemInfo.excellentOption <= 163) {
            // Specific excellent option
            item.m_NewOption = itemInfo.excellentOption - 100;
        }
    }
    
    // Set set item value
    if (itemInfo.setItemValue > 0) {
        switch (itemInfo.setItemValue) {
            case 1: // Ancient set
                item.m_SetOption = 4;
                break;
            case 2: // Normal set (5,9 level)
                item.m_SetOption = (GetLargeRand() % 2 == 0) ? 5 : 9;
                break;
            case 3: // Enhanced set (6,10 level)
                item.m_SetOption = (GetLargeRand() % 2 == 0) ? 6 : 10;
                break;
            case 5:
            case 6:
            case 9:
            case 10:
                // Fixed set level
                item.m_SetOption = itemInfo.setItemValue;
                break;
        }
    }
    
    // Set socket option
    if (itemInfo.socketOption > 0) {
        if (itemInfo.socketOption <= 5) {
            // Random 1~5 sockets
            int socketCount = 1 + GetLargeRand() % itemInfo.socketOption;
            for (int i = 0; i < socketCount && i < 5; i++) {
                item.m_SocketOption[i] = 1; // Empty socket
            }
        } else if (itemInfo.socketOption == 6) {
            // 5 sockets
            for (int i = 0; i < 5; i++) {
                item.m_SocketOption[i] = 1;
            }
        } else if (itemInfo.socketOption >= 11 && itemInfo.socketOption <= 15) {
            // Specific socket count
            int socketCount = itemInfo.socketOption - 10;
            for (int i = 0; i < socketCount && i < 5; i++) {
                item.m_SocketOption[i] = 1;
            }
        }
    }
    
    // Set duration
    if (itemInfo.duration > 0) {
        item.m_Duration = itemInfo.duration;
    }
    
    // Create item on map
    bool result = gItemManager.DropItem(lpTargetObj, &item, lpTargetObj->Map, lpTargetObj->X, lpTargetObj->Y, 0, 0, 0);
    
    if (result) {
        LogAdd(LOG_BLUE, "[MonsterDropConfig] Item dropped: [%s][%d,%d] Level:%d for User:%s", 
               itemInfoPtr->Name, itemInfo.itemType1, itemInfo.itemType2, 
               item.m_Level, lpTargetObj->Account);
    }
    
    return result;
}

bool CMonsterDropConfig::CheckDropCondition(LPOBJ lpTargetObj, int condition) {
    if (condition == 0) {
        return true; // No condition
    }
    
    // Check VIP level condition
    if (condition > 0 && condition <= 100) {
        return lpTargetObj->AccountLevel >= condition;
    }
    
    // Add more condition checks here if needed
    return true;
}

bool CMonsterDropConfig::CheckLevelRequirement(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo) {
    if (itemInfo.level == -1) {
        return true; // Special drop, no level requirement
    }
    
    // Check if player meets the level requirement
    // This can be based on player level, reset count, or other criteria
    int playerLevel = lpTargetObj->Level + (lpTargetObj->MasterLevel);
    
    return playerLevel >= itemInfo.level;
}

MONSTER_DROP_CONFIG* CMonsterDropConfig::GetConfig(int monsterId) {
    auto it = m_MonsterConfigs.find(monsterId);
    if (it != m_MonsterConfigs.end()) {
        return &it->second;
    }
    return nullptr;
}

bool CMonsterDropConfig::IsConfigLoaded(int monsterId) {
    return m_MonsterConfigs.find(monsterId) != m_MonsterConfigs.end();
}
