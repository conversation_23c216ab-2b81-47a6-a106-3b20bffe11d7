// Viewport.h: interface for the CViewport class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "ItemManager.h"
#include "Protocol.h"

#ifndef MAX_EFFECT_LIST
#define MAX_EFFECT_LIST 32  
#endif


enum eViewportState
{
	VIEWPORT_NONE = 0,
	VIEWPORT_SEND = 1,
	VIEWPORT_WAIT = 2,
	VIEWPORT_DESTROY = 3,
};

//**********************************************//
//************ GameServer -> Client ************//
//**********************************************//

struct PMSG_VIEWPORT_DESTROY_SEND
{
	PBMSG_HEAD header; // C1:14
	BYTE count;
};

struct PMSG_VIEWPORT_DESTROY_ITEM_SEND
{
	PWMSG_HEAD header; // C2:21
	BYTE count;
};

struct PMSG_VIEWPORT_DESTROY
{
	BYTE index[2];
};

struct PMSG_VIEWPORT_SEND
{
	PWMSG_HEAD header; // C2:[12:13:1F:20:45:65:68]
	BYTE count;
};


struct LPPCREATE_CHARACTER
{
	INDEX_DATA(Index);
	uint8_t PositionX;
	uint8_t PositionY;
	uint8_t ServerClass;
	uint8_t ActionPose;
	uint8_t Name[MAX_CHARACTER_LENGTH];
	uint32_t Equipment[EQUIPMENT_NEW_LENGTH];
	uint32_t MaxHP;
	uint32_t CurHP;
	uint8_t TX;
	uint8_t TY;
	uint8_t dir_and_pk_level;
	uint32_t MaxShield;
	uint32_t Shield;
	BYTE s_BuffCount;                      
	BYTE s_BuffEffectState[MAX_EFFECT_LIST];
};

struct LPPCREATE_TRANSFORM
{
	INDEX_DATA(Index);
	uint8_t PositionX;
	uint8_t PositionY;
	INDEX_DATA(Skin);
	uint8_t Name[MAX_CHARACTER_LENGTH];
	uint8_t ServerClass;
	uint8_t ActionPose;
	uint32_t Equipment[EQUIPMENT_NEW_LENGTH];
	uint32_t MaxHP;
	uint32_t CurHP;
	uint8_t TX;
	uint8_t TY;
	uint8_t dir_and_pk_level;
	uint32_t MaxShield;
	uint32_t Shield;
	BYTE s_BuffCount;
	BYTE s_BuffEffectState[MAX_EFFECT_LIST]; 
};


struct PMSG_VIEWPORT_MONSTER
{
	uint8_t index[2];
	uint8_t type[2];
	uint8_t x;
	uint8_t y;
	uint8_t tx;
	uint8_t ty;
	uint8_t dir_and_pk_level;
	#if(GAMESERVER_UPDATE>=701)
	uint8_t attribute;
	uint8_t level[2];
	uint8_t MaxHP[4];
	uint8_t CurHP[4];
	#endif
	uint8_t  LevelH;
	uint8_t  LevelL;
	uint32_t MaxHP;
	uint32_t CurHP;
	BYTE s_BuffCount;
};

struct PMSG_VIEWPORT_SUMMON
{
	uint8_t index[2];
	uint8_t type[2];
	uint8_t x;
	uint8_t y;
	uint8_t tx;
	uint8_t ty;
	uint8_t dir_and_pk_level;
	char name[MAX_CHARACTER_LENGTH];
	#if(GAMESERVER_UPDATE>=701)
	uint8_t attribute;
	uint8_t level[2];
	uint8_t MaxHP[4];
	uint8_t CurHP[4];
	#endif
	uint8_t  LevelH;
	uint8_t  LevelL;
	uint32_t MaxHP;
	uint32_t CurHP;
	BYTE s_BuffCount;
};

struct PMSG_VIEWPORT_ITEM
{
	BYTE index[2];
	BYTE x;
	BYTE y;
	BYTE ItemInfo[MAX_ITEM_INFO];
};

struct PMSG_VIEWPORT_GUILD
{
	DWORD number;
	BYTE status;
	BYTE type;
	BYTE relationship;
	BYTE index[2];
	#if(GAMESERVER_UPDATE>=401)
	BYTE owner;
	#endif
};

struct PMSG_VIEWPORT_UNION
{
	BYTE index[2];
	DWORD number;
	BYTE relationship;
	char UnionName[8];
};

struct PMSG_VIEWPORT_CASTLE_SIEGE_WEAPON
{
	BYTE type;
	BYTE skin[2];
	BYTE index[2];
	BYTE x;
	BYTE y;
	DWORD Equipment[EQUIPMENT_NEW_LENGTH];
	BYTE s_BuffCount;
};

struct PMSG_VIEWPORT_STATE
{
	BYTE effect;
};

//**********************************************//
//**********************************************//
//**********************************************//

class CViewport
{
public:
	CViewport();
	virtual ~CViewport();
	bool CheckViewportObjectPosition(int aIndex,int map,int x,int y,int view);
	bool CheckViewportObject1(int aIndex,int bIndex,int type);
	bool CheckViewportObject2(int aIndex,int bIndex,int type);
	bool CheckViewportObjectItem(int aIndex,int bIndex,int type);
	bool AddViewportObject1(int aIndex,int bIndex,int type);
	bool AddViewportObject2(int aIndex,int bIndex,int type);
	bool AddViewportObjectItem(int aIndex,int bIndex,int type);
	void AddViewportObjectAgro(int aIndex,int bIndex,int type);
	void DelViewportObjectAgro(int aIndex,int bIndex,int type);
	void DestroyViewportPlayer1(int aIndex);
	void DestroyViewportPlayer2(int aIndex);
	void DestroyViewportMonster1(int aIndex);
	void DestroyViewportMonster2(int aIndex);
	void DestroyViewportItem(int aIndex);
	void CreateViewportPlayer(int aIndex);
	void CreateViewportMonster(int aIndex);
	void CreateViewportItem(int aIndex);
	void GCViewportDestroySend(int aIndex);
	void GCViewportDestroyItemSend(int aIndex);
	void GCViewportPlayerSend(int aIndex);
	void GCViewportMonsterSend(int aIndex);
	void GCViewportSummonSend(int aIndex);
	void GCViewportItemSend(int aIndex);
	void GCViewportChangeSend(int aIndex);
	void GCViewportGuildSend(int aIndex);
	void GCViewportUnionSend(int aIndex);
	void GCViewportCastleSiegeWeaponSend(int aIndex,int tx,int ty);
	void GCViewportGensSystemSend(int aIndex);
	void GCViewportSimpleDestroySend(LPOBJ lpObj);
	void GCViewportSimplePlayerSend(LPOBJ lpObj);
	void GCViewportSimpleMonsterSend(LPOBJ lpObj);
	void GCViewportSimpleSummonSend(LPOBJ lpObj);
	void GCViewportSimpleChangeSend(LPOBJ lpObj);
	void GCViewportSimpleGuildSend(LPOBJ lpObj);
	void GCViewportSimpleUnionSend(LPOBJ lpObj);
	void GCViewportSimpleGensSystemSend(LPOBJ lpObj);
	bool CheckCustomEventPkViewport(LPOBJ lpObj, LPOBJ lpTarget);

	void ViewportConstructPlayer(LPPCREATE_CHARACTER* player_body, LPOBJ pPlayer);
	void ViewportConstructPlayerChange(LPPCREATE_TRANSFORM* player_body, LPOBJ pPlayer);
};

extern CViewport gViewport;