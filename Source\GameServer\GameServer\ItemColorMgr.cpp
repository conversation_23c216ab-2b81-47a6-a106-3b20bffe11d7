#include "StdAfx.h"
#include "Util.h"
#include "MemScript.h"
#include "ItemManager.h"
#include "ItemColorMgr.h"

#if COLOR_ITEM_DEF
CItemColorMgr gItemColorMgr;

CItemColorMgr::CItemColorMgr()
{
}

CItemColorMgr::~CItemColorMgr()
{
	this->Release();
}

void CItemColorMgr::Release()
{
	this->m_ItemColorInfo.clear();
	memset(this->iTableColor, 0, sizeof(this->iTableColor));
}

void CItemColorMgr::LoadFiles(char* path)
{
	CMemScript* lpMemScript = new CMemScript;

	if (lpMemScript == 0)
	{
		ErrorMessageBox(MEM_SCRIPT_ALLOC_ERROR, path);
		return;
	}

	if (lpMemScript->SetBuffer(path) == 0)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
		delete lpMemScript;
		return;
	}

	this->Release();

	try
	{
		while (true)
		{
			if (lpMemScript->GetToken() == TOKEN_END)
			{
				break;
			}

			int section = lpMemScript->GetNumber();

			while (true)
			{
				if (strcmp("end", lpMemScript->GetAsString()) == 0)
				{
					break;
				}

				if (section == 0)
				{
					MAKE_INFO_COLOR info;

					int iType = lpMemScript->GetNumber();

					int Index = lpMemScript->GetAsNumber();

					info.ItemType = GET_ITEM(iType, Index);

					info.iColor = lpMemScript->GetAsNumber();

					m_ItemColorInfo.push_back(info);
				}
				else if (section == 1)
				{
					int iType = lpMemScript->GetNumber();

					int red = lpMemScript->GetAsNumber();

					int green = lpMemScript->GetAsNumber();

					int blue = lpMemScript->GetAsNumber();

					if (iType >= 0 && iType < MAX_TABLE_COLOR)
					{
						this->iTableColor[iType] = ARGB(0, red, green, blue);
					}
				}
				else
				{
					break;
				}
			}
		}
	}
	catch (...)
	{
		ErrorMessageBox(lpMemScript->GetLastError());
	}

	delete lpMemScript;
}

uint32_t CItemColorMgr::GetColorById(int id)
{
	if (id >= 0 && id < MAX_TABLE_COLOR)
		return iTableColor[id];
	else
		return 0;
}

MAKE_INFO_COLOR* CItemColorMgr::GetColorOption(int ItemIndex)
{
	for (int i = 0; i < m_ItemColorInfo.size(); i++)
	{
		if (m_ItemColorInfo[i].ItemType == ItemIndex)
		{
			return &m_ItemColorInfo[i];
		}
	}

	return NULL;
}

bool CItemColorMgr::CharacterUseDrawColor(LPOBJ lpObj, int SourceSlot, int TargetSlot)
{
	if (INVENTORY_FULL_RANGE(SourceSlot) == 0)
	{
		return 0;
	}

	if (INVENTORY_FULL_RANGE(TargetSlot) == 0)
	{
		return 0;
	}

	if (lpObj->Inventory[SourceSlot].IsItem() == 0)
	{
		return 0;
	}

	if (lpObj->Inventory[TargetSlot].IsItem() == 0)
	{
		return 0;
	}

	CItem* lpItem = &lpObj->Inventory[SourceSlot];

	MAKE_INFO_COLOR* lpInfo = this->GetColorOption(lpItem->m_Index);

	if (lpInfo)
	{
		lpObj->Inventory[TargetSlot].m_Colorindex = lpInfo->iColor;

		return true;
	}

	return false;
}
#endif // COLOR_ITEM_DEF


