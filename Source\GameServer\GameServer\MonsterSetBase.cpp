// MonsterSetBase.cpp: implementation of the CMonsterSetBase class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "MonsterSetBase.h"
#include "MapServerManager.h"
#include "MemScript.h"
#include "Util.h"
#include "Path.h"
#include "MapManager.h"
#include "MonsterManager.h"

CMonsterSetBase gMonsterSetBase;

CMonsterSetBase::CMonsterSetBase() // OK
{
	this->ClearAll();
}

CMonsterSetBase::~CMonsterSetBase() // OK
{
	this->ClearAll();
}

void CMonsterSetBase::Load(std::string path, int _map) // OK
{
	CMemScript* lpMemScript = new CMemScript;

	if (lpMemScript == NULL)
	{
#if GAMESERVER_CLIENTE_UPDATE < 16
		ErrorMessageBox(MEM_SCRIPT_ALLOC_ERROR, path);
#endif // GAMESERVER_CLIENTE_UPDATE < 16
		return;
	}

	if (lpMemScript->SetBuffer(path.c_str()) == 0)
	{
#if GAMESERVER_CLIENTE_UPDATE < 16
		ErrorMessageBox(lpMemScript->GetLastError());
#endif // GAMESERVER_CLIENTE_UPDATE < 16
		delete lpMemScript;
		return;
	}


	try
	{
		while (true)
		{
			if (lpMemScript->GetToken() == TOKEN_END)
			{
				break;
			}

			int section = lpMemScript->GetNumber();

			while (true)
			{
				if (strcmp("end", lpMemScript->GetAsString()) == 0)
				{
					break;
				}

				MONSTER_SET_BASE_INFO info;

				memset(&info, 0, sizeof(info));

				info.Type = section;

				info.MonsterClass = lpMemScript->GetNumber();

				info.Map = lpMemScript->GetAsNumber();

				info.Dis = lpMemScript->GetAsNumber();

				info.X = lpMemScript->GetAsNumber();

				info.Y = lpMemScript->GetAsNumber();

				if (section == 1 || section == 3)
				{
					info.TX = lpMemScript->GetAsNumber();
					info.TY = lpMemScript->GetAsNumber();
				}
				else if (section == 2)
				{
#ifdef GAMESERVER_EDITH_EXPORT
					info.X = (info.X - 3) + GetLargeRand() % 7;
					info.Y = (info.Y - 3) + GetLargeRand() % 7;
#endif // !GAMESERVER_EDITH_EXPORT
				}

				info.Dir = lpMemScript->GetAsNumber();

				if (_map != -1)
				{
					info.Map = _map;
				}

				if (section == 1 || section == 3)
				{
					info.Count = lpMemScript->GetAsNumber();

					if (section == 3)
					{
						info.Value = lpMemScript->GetAsNumber();
					}

#ifdef GAMESERVER_EDITH_EXPORT
					for (int n = 0; n < info.Count; n++)
					{
						this->SetInfo(info.Map, info);
					}
#else
					this->SetInfo(info);
#endif // GAMESERVER_EDITH_EXPORT
				}
				else
				{
					this->SetInfo(info.Map, info);
				}
			}
		}
	}
	catch (...)
	{
		if (_map == -1)
		{
#if GAMESERVER_CLIENTE_UPDATE < 16
			ErrorMessageBox(lpMemScript->GetLastError());
#endif // GAMESERVER_CLIENTE_UPDATE < 16
		}
	}

	delete lpMemScript;
}

void CMonsterSetBase::LoadFiles()
{
	this->ClearAll();



#if GAMESERVER_CLIENTE_UPDATE >= 16
#if(GAMESERVER_TYPE==0)
	this->Load(gPath.GetFullPath("Monster\\MonsterSetBase.txt"));

	this->ProcessFilesInDirectory(gPath.GetFullPath("MonsterSetBase"));
#else
	this->Load(gPath.GetFullPath("Monster\\MonsterSetBaseCS.txt"));

	this->ProcessFilesInDirectory(gPath.GetFullPath("MonsterSetBaseCS"));
#endif
#else
#if(GAMESERVER_TYPE==0)
	this->Load(gPath.GetFullPath("Monster\\MonsterSetBase.txt"));
#else
	this->Load(gPath.GetFullPath("Monster\\MonsterSetBaseCS.txt"));
#endif
#endif // GAMESERVER_CLIENTE_UPDATE >= 16
}

#if GAMESERVER_CLIENTE_UPDATE >= 16
void CMonsterSetBase::ProcessFilesInDirectory(const std::string& directory)
{
	WIN32_FIND_DATA data;

	std::string wildcard_path = directory + "\\*";

	HANDLE file = FindFirstFile(wildcard_path.c_str(), &data);

	if (file != INVALID_HANDLE_VALUE)
	{
		do
		{
			std::string file_name = data.cFileName;

			if (file_name == "." || file_name == "..") {
				continue;
			}

			std::string full_path = directory + "\\" + file_name;

			if ((data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) == 0)
			{
				if ((isdigit(data.cFileName[0]) != 0 && isdigit(data.cFileName[1]) != 0 && isdigit(data.cFileName[2]) != 0)
					&& (data.cFileName[3] == ' ' && data.cFileName[4] == '-' && data.cFileName[5] == ' '))
				{
					int _map = atoi(data.cFileName);

					if (gMapServerManager.CheckMapServer(_map))
					{
						this->Load(full_path, _map);
					}
				}
			}
			else
			{
				this->ProcessFilesInDirectory(full_path);
			}
		} while (FindNextFile(file, &data) != 0);

		FindClose(file);
	}
}
#endif // GAMESERVER_CLIENTE_UPDATE >= 16

void CMonsterSetBase::ClearAll()
{
	this->m_count = 0;
	memset(m_MonsterIndexTable, -1, sizeof(m_MonsterIndexTable));

	for (size_t i = 0; i < MAX_MAP; i++)
	{
		m_MonsterSetBaseInfo[i].clear();
	}
}

void CMonsterSetBase::SetInfo(int iMap, MONSTER_SET_BASE_INFO info) // OK
{
	if ((this->m_count < 0 || this->m_count >= MAX_MSB_MONSTER))
	{
		//LogAdd(LOG_BLACK, "[ERROR] MonsterSetBase - MAX_MSB_MONSTER: %d", this->m_count);
		return;
	}


	if ((iMap < 0 || iMap >= MAX_MAP))
	{
		//LogAdd(LOG_BLACK, "[ERROR] MonsterSetBase - out range map: %d", iMap);
		return;
	}

	if (gMapServerManager.CheckMapServer(iMap) == 0)
	{
		//LogAdd(LOG_BLACK, "[ERROR] MonsterSetBase - out server map: %d", iMap);
		return;
	}

	info.Dir = ((info.Dir == -1) ? (GetLargeRand() % 8) : info.Dir);
	info.index = this->m_count++;
	m_MonsterSetBaseInfo[iMap].push_back(info);

	int targetIndex = m_MonsterSetBaseInfo[iMap].size() - 1;
	m_MonsterIndexTable[info.index] = MONSTER_SET_REFLINK(targetIndex, iMap);
}

bool CMonsterSetBase::GetPosition(int index, short map, short* ox, short* oy) // OK
{
	if (index < 0 || index >= MAX_MSB_MONSTER)
	{
		return 0;
	}

	MONSTER_SET_BASE_INFO* lpInfo = this->FindMonsterByIndex(index);

	if (lpInfo)
	{
		return lpInfo->GetPosition(ox, oy);
	}

	return 0;
}

bool CMonsterSetBase::GetBoxPosition(int map, int x, int y, int tx, int ty, short* ox, short* oy) // OK
{
	for (int n = 0; n < 100; n++)
	{
		int subx = tx - x;
		int suby = ty - y;

		subx = ((subx < 1) ? 1 : subx);
		suby = ((suby < 1) ? 1 : suby);

		subx = x + (GetLargeRand() % subx);
		suby = y + (GetLargeRand() % suby);

		if (gMap[map].CheckAttr(subx, suby, 1) == 0 && gMap[map].CheckAttr(subx, suby, 4) == 0 && gMap[map].CheckAttr(subx, suby, 8) == 0)
		{
			(*ox) = subx;
			(*oy) = suby;
			return 1;
		}
	}

	return 0;
}

MONSTER_SET_BASE_INFO* CMonsterSetBase::FindMonsterByIndex(int _index)
{
	MONSTER_SET_BASE_INFO* dataByIndex = NULL;

	if (_index >= 0 && _index < MAX_MSB_MONSTER)
	{
		int targetIndex = m_MonsterIndexTable[_index].index;
		int targetMapes = m_MonsterIndexTable[_index].map;

		if (targetIndex > 0 && targetMapes >= 0 && targetMapes < MAX_MAP)
		{
			dataByIndex = &m_MonsterSetBaseInfo[targetMapes][targetIndex];
		}
	}

	return dataByIndex;
}

std::vector<MONSTER_SET_BASE_INFO>& CMonsterSetBase::FindDataMonsterByMap(int _map)
{
	static std::vector<MONSTER_SET_BASE_INFO> emptyList;

	if (_map < 0 || _map >= MAX_MAP)
	{
		return emptyList;
	}

	return m_MonsterSetBaseInfo[_map];
}

MONSTER_SET_BASE_INFO* CMonsterSetBase::FindMonsterByMap(int _map, int _index)
{
	if (_map < 0 || _map >= MAX_MAP)
	{
		return NULL;
	}

	std::vector<MONSTER_SET_BASE_INFO>& MonsterEntry = m_MonsterSetBaseInfo[_map];

	auto find_monster = std::find_if(MonsterEntry.begin(), MonsterEntry.end(), [_index](const MONSTER_SET_BASE_INFO& monster)
		{
			return monster.index == _index;
		});

	return (find_monster != MonsterEntry.end()) ? &(*find_monster) : NULL;
}

bool MONSTER_SET_BASE_INFO::GetPosition(short* ox, short* oy)
{
	if (this->Type == 0 || this->Type == 4)
	{
		(*ox) = this->X;
		(*oy) = this->Y;
		return 1;
	}
	else if (this->Type == 1 || this->Type == 3)
	{
		return this->GetBoxPosition(this->X, this->Y, this->TX, this->TY, ox, oy);
	}
	else if (this->Type == 2)
	{
		return this->GetBoxPosition((this->X - 3), (this->Y - 3), (this->X + 3), (this->Y + 3), ox, oy);
	}

	return false;
}

bool MONSTER_SET_BASE_INFO::GetBoxPosition(int x, int y, int tx, int ty, short* ox, short* oy)
{
	for (int n = 0; n < 100; n++)
	{
		int subx = tx - x;
		int suby = ty - y;

		subx = ((subx < 1) ? 1 : subx);
		suby = ((suby < 1) ? 1 : suby);

		subx = x + (GetLargeRand() % subx);
		suby = y + (GetLargeRand() % suby);

		if (gMap[this->Map].CheckAttr(subx, suby, 1) == 0 && gMap[this->Map].CheckAttr(subx, suby, 4) == 0 && gMap[this->Map].CheckAttr(subx, suby, 8) == 0)
		{
			(*ox) = subx;
			(*oy) = suby;
			return 1;
		}
	}

	return 0;
}
