# MonsterDropSystem 模块开发文档

## 1. 概述

本文档描述了针对`Monster_1000.txt`配置文件的完整模块实现，该配置文件用于定义ID为1000的怪物的物品掉落规则。该模块提供了灵活的掉落配置系统，支持基于怪物等级、玩家等级、物品稀有度等多种条件的掉落控制。

## 2. 配置文件格式分析

### 2.1 文件结构
配置文件分为4个主要段落：
- **段0**：文件基本信息和描述
- **段1**：怪物基础配置（等级、掉落类型等）
- **段2**：掉落等级分组配置
- **段3**：具体物品掉落配置

### 2.2 各段详细结构

#### 段0 - 基本信息段
```
0
    "怪物描述信息"
end
```

#### 段1 - 怪物基础配置段
```
1
    怪物等级  是否使用固定掉落  固定掉落数量  掉落等级  掉落范围  预留1  预留2  预留3
end
```
**字段说明：**
- `怪物等级`: 怪物的基础等级
- `是否使用固定掉落`: 1=使用固定掉落，0=使用随机掉落
- `固定掉落数量`: 当使用固定掉落时的掉落数量
- `掉落等级`: 掉落物品的基础等级
- `掉落范围`: 掉落物品等级的浮动范围

#### 段2 - 掉落等级分组段
```
2
    使用等级  掉落率  追加方式  优越选项  卓越选项  套装选项  镶嵌选项  最小等级  最大等级  预留1  预留2
end
```

#### 段3 - 具体物品配置段
```
3
    等级  类型1  类型2  最小等级  最大等级  技能率  幸运率  追加  品质  优越  套装值  镶嵌  使用时间  掉落条件  预留  掉落率
end
```

## 3. 数据结构设计

### 3.1 核心数据结构

```cpp
// 基础配置信息结构
struct MONSTER_DROP_BASE_INFO {
    int monsterLevel;           // 怪物等级
    int useFixedDrop;          // 是否使用固定掉落 (0/1)
    int fixedDropCount;        // 固定掉落数量
    int dropLevel;             // 掉落等级
    int dropRange;             // 掉落范围
    int reserved1;             // 预留字段1
    int reserved2;             // 预留字段2
    int reserved3;             // 预留字段3
};

// 掉落等级分组配置
struct MONSTER_DROP_LEVEL_GROUP {
    int useLevel;              // 使用等级 (-1表示特定掉落)
    int dropRate;              // 掉落率
    int additionalMethod;      // 追加方式
    int excellentOption;       // 优越选项
    int ancientOption;         // 卓越选项
    int setOption;             // 套装选项
    int socketOption;          // 镶嵌选项
    int minLevel;              // 最小等级
    int maxLevel;              // 最大等级
    int reserved1;             // 预留字段1
    int reserved2;             // 预留字段2
};

// 具体物品掉落配置
struct MONSTER_DROP_ITEM_INFO {
    int level;                 // 等级 (-1表示特定掉落)
    int itemType1;             // 物品类型1
    int itemType2;             // 物品类型2
    int minLevel;              // 最小等级
    int maxLevel;              // 最大等级
    int skillRate;             // 技能率 (0-100)
    int luckRate;              // 幸运率 (0-100)
    int addOption;             // 追加选项 (0-7)
    int quality;               // 品质 (0-255)
    int excellentOption;       // 优越选项
    int setItemValue;          // 套装值
    int socketOption;          // 镶嵌选项
    int duration;              // 使用时间 (分钟)
    int dropCondition;         // 掉落条件 (VIP等级限制)
    int reserved;              // 预留字段
    int dropRate;              // 掉落率
};

// 完整的怪物掉落配置
struct MONSTER_DROP_CONFIG {
    int monsterId;             // 怪物ID
    std::string description;   // 怪物描述
    MONSTER_DROP_BASE_INFO baseInfo;
    std::vector<MONSTER_DROP_LEVEL_GROUP> levelGroups;
    std::vector<MONSTER_DROP_ITEM_INFO> dropItems;
};
```

### 3.2 管理器类设计

```cpp
/**
 * @class CMonsterDropConfig
 * @brief 怪物掉落配置管理器
 * 
 * 该类负责加载、解析和管理怪物掉落配置文件，
 * 提供基于配置的物品掉落功能。
 */
class CMonsterDropConfig {
public:
    CMonsterDropConfig();
    virtual ~CMonsterDropConfig();
    
    /**
     * @brief 加载指定怪物的掉落配置文件
     * @param monsterId 怪物ID
     * @param filePath 配置文件路径
     * @return true=成功, false=失败
     */
    bool LoadConfig(int monsterId, const char* filePath);
    
    /**
     * @brief 处理怪物掉落
     * @param lpMonsterObj 怪物对象
     * @param lpTargetObj 目标玩家对象
     * @return true=有掉落, false=无掉落
     */
    bool ProcessMonsterDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj);
    
    /**
     * @brief 获取怪物配置信息
     * @param monsterId 怪物ID
     * @return 配置信息指针，未找到返回nullptr
     */
    MONSTER_DROP_CONFIG* GetConfig(int monsterId);
    
    /**
     * @brief 清理所有配置数据
     */
    void Clear();

private:
    /**
     * @brief 解析配置文件
     * @param filePath 文件路径
     * @param config 输出的配置对象
     * @return true=成功, false=失败
     */
    bool ParseConfigFile(const char* filePath, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief 解析段0 - 基本信息
     * @param lpMemScript 内存脚本对象
     * @param config 配置对象
     * @return true=成功, false=失败
     */
    bool ParseSection0(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief 解析段1 - 基础配置
     * @param lpMemScript 内存脚本对象
     * @param config 配置对象
     * @return true=成功, false=失败
     */
    bool ParseSection1(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief 解析段2 - 等级分组
     * @param lpMemScript 内存脚本对象
     * @param config 配置对象
     * @return true=成功, false=失败
     */
    bool ParseSection2(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief 解析段3 - 物品配置
     * @param lpMemScript 内存脚本对象
     * @param config 配置对象
     * @return true=成功, false=失败
     */
    bool ParseSection3(CMemScript* lpMemScript, MONSTER_DROP_CONFIG& config);
    
    /**
     * @brief 计算掉落率
     * @param baseRate 基础掉落率
     * @param lpTargetObj 目标玩家
     * @param lpMonsterObj 怪物对象
     * @return 最终掉落率
     */
    int CalculateDropRate(int baseRate, LPOBJ lpTargetObj, LPOBJ lpMonsterObj);
    
    /**
     * @brief 创建掉落物品
     * @param lpTargetObj 目标玩家
     * @param itemInfo 物品信息
     * @return true=成功, false=失败
     */
    bool CreateDropItem(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo);
    
    /**
     * @brief 检查掉落条件
     * @param lpTargetObj 目标玩家
     * @param condition 掉落条件
     * @return true=满足条件, false=不满足
     */
    bool CheckDropCondition(LPOBJ lpTargetObj, int condition);

private:
    std::map<int, MONSTER_DROP_CONFIG> m_MonsterConfigs;
    bool m_bInitialized;
};
```

## 4. 实现细节

### 4.1 配置文件解析实现

```cpp
bool CMonsterDropConfig::ParseConfigFile(const char* filePath, MONSTER_DROP_CONFIG& config) {
    CMemScript* lpMemScript = new CMemScript;
    
    if (lpMemScript == nullptr) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Memory allocation failed");
        return false;
    }
    
    if (lpMemScript->SetBuffer(filePath) == 0) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Failed to load file: %s", filePath);
        delete lpMemScript;
        return false;
    }
    
    try {
        int currentSection = 0;
        
        while (true) {
            if (lpMemScript->GetToken() == TOKEN_END) {
                break;
            }
            
            if (strcmp("end", lpMemScript->GetString()) == 0) {
                continue;
            }
            
            // 读取段编号
            currentSection = lpMemScript->GetNumber();
            
            switch (currentSection) {
                case 0:
                    if (!ParseSection0(lpMemScript, config)) {
                        delete lpMemScript;
                        return false;
                    }
                    break;
                case 1:
                    if (!ParseSection1(lpMemScript, config)) {
                        delete lpMemScript;
                        return false;
                    }
                    break;
                case 2:
                    if (!ParseSection2(lpMemScript, config)) {
                        delete lpMemScript;
                        return false;
                    }
                    break;
                case 3:
                    if (!ParseSection3(lpMemScript, config)) {
                        delete lpMemScript;
                        return false;
                    }
                    break;
                default:
                    LogAdd(LOG_YELLOW, "[MonsterDropConfig] Unknown section: %d", currentSection);
                    break;
            }
        }
    }
    catch (...) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Exception during parsing: %s", lpMemScript->GetLastError());
        delete lpMemScript;
        return false;
    }
    
    delete lpMemScript;
    return true;
}
```

### 4.2 掉落处理逻辑

```cpp
bool CMonsterDropConfig::ProcessMonsterDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj) {
    if (!m_bInitialized || !OBJECT_RANGE(lpMonsterObj->Index) || !OBJECT_RANGE(lpTargetObj->Index)) {
        return false;
    }
    
    auto configIt = m_MonsterConfigs.find(lpMonsterObj->Class);
    if (configIt == m_MonsterConfigs.end()) {
        return false; // 该怪物没有配置
    }
    
    MONSTER_DROP_CONFIG& config = configIt->second;
    
    // 根据配置的掉落类型处理
    if (config.baseInfo.useFixedDrop == 1) {
        // 固定掉落模式
        return ProcessFixedDrop(lpMonsterObj, lpTargetObj, config);
    } else {
        // 随机掉落模式
        return ProcessRandomDrop(lpMonsterObj, lpTargetObj, config);
    }
}

bool CMonsterDropConfig::ProcessRandomDrop(LPOBJ lpMonsterObj, LPOBJ lpTargetObj, 
                                         const MONSTER_DROP_CONFIG& config) {
    bool hasDropped = false;
    
    // 遍历所有配置的掉落物品
    for (const auto& itemInfo : config.dropItems) {
        // 检查掉落条件
        if (!CheckDropCondition(lpTargetObj, itemInfo.dropCondition)) {
            continue;
        }
        
        // 检查等级限制
        if (itemInfo.level >= 0 && !CheckLevelRequirement(lpTargetObj, itemInfo)) {
            continue;
        }
        
        // 计算实际掉落率
        int finalDropRate = CalculateDropRate(itemInfo.dropRate, lpTargetObj, lpMonsterObj);
        
        // 掉落判定
        if (GetLargeRand() % 10000 < finalDropRate) {
            if (CreateDropItem(lpTargetObj, itemInfo)) {
                hasDropped = true;
                
                // 如果是使用固定掉落数量，检查是否已达到限制
                if (config.baseInfo.useFixedDrop == 1) {
                    static int dropCount = 0;
                    dropCount++;
                    if (dropCount >= config.baseInfo.fixedDropCount) {
                        break;
                    }
                }
            }
        }
    }
    
    return hasDropped;
}
```

### 4.3 物品创建实现

```cpp
bool CMonsterDropConfig::CreateDropItem(LPOBJ lpTargetObj, const MONSTER_DROP_ITEM_INFO& itemInfo) {
    CItem item;
    
    // 设置基本物品信息
    item.m_Index = GET_ITEM(itemInfo.itemType1, itemInfo.itemType2);
    
    // 验证物品有效性
    ITEM_INFO* itemInfoPtr = gItemManager.GetInfo(item.m_Index);
    if (itemInfoPtr == nullptr) {
        LogAdd(LOG_RED, "[MonsterDropConfig] Invalid item: %d,%d", 
               itemInfo.itemType1, itemInfo.itemType2);
        return false;
    }
    
    // 设置物品等级
    int itemLevel = itemInfo.minLevel;
    if (itemInfo.maxLevel > itemInfo.minLevel) {
        itemLevel += GetLargeRand() % (itemInfo.maxLevel - itemInfo.minLevel + 1);
    }
    item.m_Level = min(itemLevel, 15);
    
    // 设置技能选项
    if (itemInfo.skillRate > 0 && (GetLargeRand() % 100) < itemInfo.skillRate) {
        item.m_Option1 = 1;
    }
    
    // 设置幸运选项
    if (itemInfo.luckRate > 0 && (GetLargeRand() % 100) < itemInfo.luckRate) {
        item.m_Option2 = 1;
    }
    
    // 设置追加选项
    if (itemInfo.addOption > 0) {
        if (itemInfo.addOption <= 7) {
            // 随机追加 0~(addOption*4)
            item.m_Option3 = GetLargeRand() % (itemInfo.addOption * 4 + 1);
        } else if (itemInfo.addOption == 8) {
            // 固定追加28
            item.m_Option3 = 28;
        } else if (itemInfo.addOption >= 11 && itemInfo.addOption <= 17) {
            // 固定追加 4~28
            item.m_Option3 = 4 + (itemInfo.addOption - 11) * 4;
        }
    }
    
    // 设置品质
    if (itemInfo.quality > 0 && itemInfo.quality <= 255) {
        item.m_JewelOfHarmonyOption = itemInfo.quality;
    }
    
    // 设置优越选项
    if (itemInfo.excellentOption > 0) {
        if (itemInfo.excellentOption <= 6) {
            // 随机1~6个优越
            int excellentCount = 1 + GetLargeRand() % itemInfo.excellentOption;
            item.m_NewOption = gItemOptionRate.GetRandomItemOption(excellentCount);
        } else if (itemInfo.excellentOption == 7) {
            // 全优越
            item.m_NewOption = 63; // 111111 in binary
        } else if (itemInfo.excellentOption >= 101 && itemInfo.excellentOption <= 163) {
            // 指定优越选项
            item.m_NewOption = itemInfo.excellentOption - 100;
        }
    }
    
    // 设置套装值
    if (itemInfo.setItemValue > 0) {
        switch (itemInfo.setItemValue) {
            case 1: // 古代套装
                item.m_SetOption = 4;
                break;
            case 2: // 普通套装(5,9级)
                item.m_SetOption = (GetLargeRand() % 2 == 0) ? 5 : 9;
                break;
            case 3: // 强化套装(6,10级)
                item.m_SetOption = (GetLargeRand() % 2 == 0) ? 6 : 10;
                break;
            case 5:
            case 6:
            case 9:
            case 10:
                // 固定套装等级
                item.m_SetOption = itemInfo.setItemValue;
                break;
        }
    }
    
    // 设置镶嵌选项
    if (itemInfo.socketOption > 0) {
        if (itemInfo.socketOption <= 5) {
            // 随机1~5个插槽
            int socketCount = 1 + GetLargeRand() % itemInfo.socketOption;
            for (int i = 0; i < socketCount && i < 5; i++) {
                item.m_SocketOption[i] = 1; // 空插槽
            }
        } else if (itemInfo.socketOption == 6) {
            // 5个插槽
            for (int i = 0; i < 5; i++) {
                item.m_SocketOption[i] = 1;
            }
        } else if (itemInfo.socketOption >= 11 && itemInfo.socketOption <= 15) {
            // 指定插槽数量
            int socketCount = itemInfo.socketOption - 10;
            for (int i = 0; i < socketCount && i < 5; i++) {
                item.m_SocketOption[i] = 1;
            }
        }
    }
    
    // 设置使用时间
    if (itemInfo.duration > 0) {
        item.m_Duration = itemInfo.duration;
    }
    
    // 在地图上创建物品
    bool result = gObjCreateItemOnMap(lpTargetObj->Map, lpTargetObj->X, lpTargetObj->Y, &item);
    
    if (result) {
        LogAdd(LOG_BLUE, "[MonsterDropConfig] Item dropped: [%s][%d,%d] Level:%d for User:%s", 
               itemInfoPtr->Name, itemInfo.itemType1, itemInfo.itemType2, 
               item.m_Level, lpTargetObj->Account);
    }
    
    return result;
}
```

## 5. 集成方案

### 5.1 在现有系统中集成

在`Monster.cpp`的`gObjMonsterItemDrop`函数中添加配置文件掉落处理：

```cpp
void gObjMonsterItemDrop(LPOBJ lpObj, LPOBJ lpTarget) {
    // ... 现有代码 ...
    
    // 添加配置文件掉落处理
    if (gMonsterDropConfig.ProcessMonsterDrop(lpObj, lpTarget)) {
        return; // 如果配置文件处理了掉落，则不继续其他掉落逻辑
    }
    
    // ... 继续现有的掉落逻辑 ...
}
```

### 5.2 初始化代码

在`ServerInfo.cpp`的适当位置添加初始化：

```cpp
void CServerInfo::ReadMonsterInfo() {
    // ... 现有代码 ...
    
    // 加载怪物掉落配置
    gMonsterDropConfig.LoadConfig(1000, gPath.GetFullPath("ItemDrop\\Monster\\Monster_1000.txt"));
    
    LogAdd(LOG_GREEN, "[MonsterDropConfig] Monster drop configurations loaded");
}
```

## 6. 配置文件说明

### 6.1 掉落率说明
- 掉落率基数为10000（即1%=100）
- 支持多种掉落率修正：
  - VIP等级加成
  - 地图掉落率加成
  - 特殊状态加成

### 6.2 等级限制说明
- `-1`表示特定掉落，无等级限制
- `0~n`表示需要对应的掉落等级才能获得该物品

### 6.3 物品属性说明
- **技能率/幸运率**: 0-100的百分比
- **追加选项**: 0=关闭，1~7=随机追加0~28，8=固定追加28，11~17=固定追加4~28
- **优越选项**: 0=关闭，1~6=随机1~6个优越，7=全优越，101~163=指定优越组合
- **套装值**: 0=关闭，1=古代套装，2=普通套装，3=强化套装，5,6,9,10=固定套装等级

## 7. 性能优化建议

### 7.1 缓存优化
- 使用`std::unordered_map`替代`std::map`提高查找性能
- 预先计算累积权重，使用二分查找选择物品

### 7.2 内存优化
- 使用对象池减少频繁的内存分配
- 延迟加载配置文件，仅在需要时加载

### 7.3 算法优化
- 使用快速随机数生成器
- 批量处理掉落判定减少函数调用开销

## 8. 错误处理和日志

### 8.1 错误处理
- 配置文件格式错误处理
- 无效物品ID处理
- 内存不足处理

### 8.2 日志记录
- 配置加载日志
- 掉落成功日志
- 错误和警告日志

## 9. 测试建议

### 9.1 单元测试
- 配置文件解析测试
- 掉落率计算测试
- 物品创建测试

### 9.2 集成测试
- 与现有掉落系统兼容性测试
- 性能压力测试
- 多怪物并发掉落测试

## 10. 扩展性考虑

### 10.1 多怪物支持
- 支持批量加载多个怪物的配置文件
- 支持配置文件热重载

### 10.2 新功能扩展
- 条件掉落（如特定时间、地图等）
- 组队掉落分配
- 掉落物品绑定机制

## 结论

本模块提供了完整的怪物掉落配置文件解决方案，具有良好的扩展性和性能。通过标准化的配置格式和健壮的解析机制，可以轻松配置复杂的掉落规则，满足游戏运营的各种需求。