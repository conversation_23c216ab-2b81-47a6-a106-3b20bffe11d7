// MasterSkillTree.h: interface for the CMasterSkillTree class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "DefaultClassInfo.h"
#include "Protocol.h"
#include "SkillManager.h"
#include "User.h"

#if(GAMESERVER_UPDATE>=602)
#define MAX_SKILL_TREE_RANK 9
#define MAX_SKILL_TREE_TIER 4
#define MAX_SKILL_TREE_GROUP 3
#define MIN_SKILL_TREE_LEVEL 10
#define MAX_SKILL_TREE_LEVEL 20
#define MAX_CHARACTER_MASTER_LEVEL 1000
#else
#define MAX_SKILL_TREE_RANK 6
#define MAX_SKILL_TREE_TIER 4
#define MAX_SKILL_TREE_GROUP 4
#define MIN_SKILL_TREE_LEVEL 5
#define MAX_SKILL_TREE_LEVEL 5
#define MAX_CHARACTER_MASTER_LEVEL 1000
#endif

enum eMasterSkillNumber
{
	#if(GAMESERVER_UPDATE>=602)
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE1 = 300,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP1 = 301,
	MASTER_SKILL_ADD_MAX_SD1 = 302,
	MASTER_SKILL_ADD_MP_RECOVERY_RATE1 = 303,
	MASTER_SKILL_ADD_POISON_RESISTANCE1 = 304,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE1 = 305,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE1 = 306,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE1 = 307,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE1 = 308,
	MASTER_SKILL_ADD_DEFENSE1 = 309,
	MASTER_SKILL_ADD_BP_RECOVERY_RATE1 = 310,
	MASTER_SKILL_ADD_ICE_RESISTANCE1 = 311,
	MASTER_SKILL_ADD_GUARDIAN_DURABILITY_RATE1 = 312,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE1 = 313,
	MASTER_SKILL_ADD_ARMOR_SET_BONUS1 = 315,
	MASTER_SKILL_ADD_REFLECT_DAMAGE1 = 316,
	MASTER_SKILL_ADD_ENERGY1 = 317,
	MASTER_SKILL_ADD_VITALITY1 = 318,
	MASTER_SKILL_ADD_DEXTERITY1 = 319,
	MASTER_SKILL_ADD_STRENGTH1 = 320,
	MASTER_SKILL_ADD_DK_WING_DEFENSE = 322,
	MASTER_SKILL_ADD_DK_WING_DAMAGE = 324,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE1 = 325,
	MASTER_SKILL_ADD_CYCLONE_IMPROVED1 = 326,
	MASTER_SKILL_ADD_SLASH_IMPROVED = 327,
	MASTER_SKILL_ADD_FALLING_SLASH_IMPROVED = 328,
	MASTER_SKILL_ADD_LUNGE_IMPROVED = 329,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1 = 330,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED = 331,
	MASTER_SKILL_ADD_TWISTING_SLASH_ENHANCED = 332,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_ENHANCED = 333,
	MASTER_SKILL_ADD_MAX_HP1 = 334,
	MASTER_SKILL_ADD_WEAPON_DAMAGE1 = 335,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED = 336,
	MASTER_SKILL_ADD_FROZEN_STAB_IMPROVED = 337,
	MASTER_SKILL_ADD_MAX_MP1 = 338,
	MASTER_SKILL_ADD_DEATH_STAB_ENHANCED = 339,
	MASTER_SKILL_ADD_FROZEN_STAB_ENHANCED = 340,
	MASTER_SKILL_ADD_MAX_BP1 = 341,
	MASTER_SKILL_ADD_DEATH_STAB_MASTERED = 342,
	MASTER_SKILL_ADD_FROZEN_STAB_MASTERED = 343,
	MASTER_SKILL_ADD_BLOOD_STORM = 344,
	MASTER_SKILL_ADD_COMBO_DAMAGE = 345,
	MASTER_SKILL_ADD_BLOOD_STORM_IMPROVED = 346,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP1 = 347,
	MASTER_SKILL_ADD_TWO_HAND_SWORD_DAMAGE = 348,
	MASTER_SKILL_ADD_ONE_HAND_SWORD_DAMAGE = 349,
	MASTER_SKILL_ADD_MACE_DAMAGE = 350,
	MASTER_SKILL_ADD_SPEAR_DAMAGE = 351,
	MASTER_SKILL_ADD_TWO_HAND_SWORD_MASTERY = 352,
	MASTER_SKILL_ADD_ONE_HAND_SWORD_MASTERY = 353,
	MASTER_SKILL_ADD_MACE_MASTERY = 354,
	MASTER_SKILL_ADD_SPEAR_MASTERY = 355,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED = 356,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE1 = 357,
	MASTER_SKILL_ADD_HUNT_SD1 = 358,
	MASTER_SKILL_ADD_HUNT_HP1 = 359,
	MASTER_SKILL_ADD_GREATER_LIFE_ENHANCED = 360,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE1 = 361,
	MASTER_SKILL_ADD_HUNT_MP1 = 362,
	MASTER_SKILL_ADD_GREATER_LIFE_MASTERED = 363,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE1 = 364,
	MASTER_SKILL_ADD_CRITICAL_DAMAGE_RATE1 = 366,
	MASTER_SKILL_ADD_RESTORE_MP_RATE1 = 367,
	MASTER_SKILL_ADD_RESTORE_HP_RATE1 = 368,
	MASTER_SKILL_ADD_EXCELLENT_DAMAGE_RATE1 = 369,
	MASTER_SKILL_ADD_DOUBLE_DAMAGE_RATE1 = 370,
	MASTER_SKILL_ADD_IGNORE_DEFENSE_RATE1 = 371,
	MASTER_SKILL_ADD_RESTORE_SD_RATE1 = 372,
	MASTER_SKILL_ADD_DW_WING_DEFENSE = 375,
	MASTER_SKILL_ADD_DW_WING_DAMAGE = 377,
	MASTER_SKILL_ADD_FLAME_IMPROVED1 = 378,
	MASTER_SKILL_ADD_LIGHTNING_IMPROVED1 = 379,
	MASTER_SKILL_ADD_MAGIC_CIRCLE_IMPROVED = 380,
	MASTER_SKILL_ADD_INFERNO_IMPROVED1 = 381,
	MASTER_SKILL_ADD_BLAST_IMPROVED1 = 382,
	MASTER_SKILL_ADD_MAGIC_CIRCLE_ENHANCED = 383,
	MASTER_SKILL_ADD_POISON_IMPROVED = 384,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1 = 385,
	MASTER_SKILL_ADD_MAGIC_DAMAGE1 = 386,
	MASTER_SKILL_ADD_DECAY_IMPROVED = 387,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED = 388,
	MASTER_SKILL_ADD_ICE_IMPROVED1 = 389,
	MASTER_SKILL_ADD_METEORITE_IMPROVED = 390,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED = 391,
	MASTER_SKILL_ADD_NOVA_IMPROVED = 392,
	MASTER_SKILL_ADD_ICE_STORM_ENHANCED = 393,
	MASTER_SKILL_ADD_METEORITE_ENHANCED = 394,
	MASTER_SKILL_ADD_NOVA_START_IMPROVED = 395,
	MASTER_SKILL_ADD_ONE_HAND_STAFF_DAMAGE = 397,
	MASTER_SKILL_ADD_TWO_HAND_STAFF_DAMAGE = 398,
	MASTER_SKILL_ADD_SHIELD_DEFENSE1 = 399,
	MASTER_SKILL_ADD_ONE_HAND_STAFF_MASTERY = 400,
	MASTER_SKILL_ADD_TWO_HAND_STAFF_MASTERY = 401,
	MASTER_SKILL_ADD_SHIELD_MASTERY1 = 402,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED = 403,
	MASTER_SKILL_ADD_MANA_SHIELD_ENHANCED = 404,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE1 = 405,
	MASTER_SKILL_ADD_MANA_SHIELD_MASTERED = 406,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE1 = 407,
	MASTER_SKILL_ADD_FE_WING_DEFENSE = 410,
	MASTER_SKILL_ADD_FIVE_SHOT_IMPROVED = 411,
	MASTER_SKILL_ADD_FE_WING_DAMAGE = 412,
	MASTER_SKILL_ADD_HEAL_IMPROVED = 413,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED = 414,
	MASTER_SKILL_ADD_SUMMON_DEFENSE = 415,
	MASTER_SKILL_ADD_PENETRATION_IMPROVED = 416,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED = 417,
	MASTER_SKILL_ADD_TRIPLE_SHOT_ENHANCED = 418,
	MASTER_SKILL_ADD_SUMMON_LIFE = 419,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED = 420,
	MASTER_SKILL_ADD_WEAPON_DAMAGE2 = 421,
	MASTER_SKILL_ADD_GREATER_DAMAGE_ENHANCED = 422,
	MASTER_SKILL_ADD_GREATER_DEFENSE_ENHANCED = 423,
	MASTER_SKILL_ADD_ICE_ARROW_IMPROVED = 424,
	MASTER_SKILL_ADD_CURE = 425,
	MASTER_SKILL_ADD_PARTY_HEAL = 426,
	MASTER_SKILL_ADD_POISON_ARROW = 427,
	MASTER_SKILL_ADD_SUMMON_DAMAGE = 428,
	MASTER_SKILL_ADD_PARTY_HEAL_IMPROVED = 429,
	MASTER_SKILL_ADD_BLESS = 430,
	MASTER_SKILL_ADD_FIVE_SHOT_ENHANCED = 431,
	MASTER_SKILL_ADD_SUMMON_SATYROS = 432,
	MASTER_SKILL_ADD_BLESS_IMPROVED = 433,
	MASTER_SKILL_ADD_POISON_ARROW_IMPROVED = 434,
	MASTER_SKILL_ADD_BOW_DAMAGE = 435,
	MASTER_SKILL_ADD_CROSS_BOW_DAMAGE = 436,
	MASTER_SKILL_ADD_SHIELD_DEFENSE2 = 437,
	MASTER_SKILL_ADD_BOW_MASTERY = 438,
	MASTER_SKILL_ADD_CROSS_BOW_MASTERY = 439,
	MASTER_SKILL_ADD_SHIELD_MASTERY2 = 440,
	MASTER_SKILL_ADD_INFINITY_ARROW_IMPROVED = 441,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE2 = 442,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE2 = 443,
	MASTER_SKILL_ADD_SU_WING_DEFENSE = 446,
	MASTER_SKILL_ADD_SU_WING_DAMAGE = 447,
	MASTER_SKILL_ADD_SAHAMUTT_IMPROVED = 448,
	MASTER_SKILL_ADD_NEIL_IMPROVED = 449,
	MASTER_SKILL_ADD_GHOST_PHANTOM_IMPROVED = 450,
	MASTER_SKILL_ADD_SAHAMUTT_ENHANCED = 451,
	MASTER_SKILL_ADD_NEIL_ENHANCED = 452,
	MASTER_SKILL_ADD_GHOST_PHANTOM_ENHANCED = 453,
	MASTER_SKILL_ADD_SLEEP_IMPROVED = 454,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED = 455,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED = 456,
	MASTER_SKILL_ADD_MAGIC_DAMAGE2 = 457,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED = 458,
	MASTER_SKILL_ADD_LESSER_DAMAGE_IMPROVED = 459,
	MASTER_SKILL_ADD_LESSER_DEFENSE_IMPROVED = 460,
	MASTER_SKILL_ADD_BLIND = 461,
	MASTER_SKILL_ADD_DRAIN_LIFE_ENHANCED = 462,
	MASTER_SKILL_ADD_BLIND_IMPROVED = 463,
	MASTER_SKILL_ADD_STICK_DAMAGE = 465,
	MASTER_SKILL_ADD_BOOK_DAMAGE = 466,
	MASTER_SKILL_ADD_STICK_MASTERY = 467,
	MASTER_SKILL_ADD_BOOK_MASTERY = 468,
	MASTER_SKILL_ADD_SWORD_POWER_IMPROVED = 469,
	MASTER_SKILL_ADD_SWORD_POWER_ENHANCED = 470,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE2 = 471,
	MASTER_SKILL_ADD_SWORD_POWER_MASTERED = 472,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE2 = 473,
	MASTER_SKILL_ADD_MG_WING_DEFENSE = 476,
	MASTER_SKILL_ADD_MG_WING_DAMAGE = 478,
	MASTER_SKILL_ADD_CYCLONE_IMPROVED2 = 479,
	MASTER_SKILL_ADD_LIGHTNING_IMPROVED2 = 480,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2 = 481,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED = 482,
	MASTER_SKILL_ADD_FLAME_IMPROVED2 = 483,
	MASTER_SKILL_ADD_BLAST_IMPROVED2 = 484,
	MASTER_SKILL_ADD_WEAPON_DAMAGE3 = 485,
	MASTER_SKILL_ADD_INFERNO_IMPROVED2 = 486,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2 = 487,
	MASTER_SKILL_ADD_MAGIC_DAMAGE3 = 488,
	MASTER_SKILL_ADD_ICE_IMPROVED2 = 489,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED = 490,
	MASTER_SKILL_ADD_ICE_ENHANCED = 491,
	MASTER_SKILL_ADD_SWORD_SLASH_IMPROVED = 492,
	MASTER_SKILL_ADD_FIRE_SLASH_ENHANCED = 493,
	MASTER_SKILL_ADD_SWORD_SLASH_ENHANCED = 494,
	MASTER_SKILL_ADD_EARTH_PRISON = 495,
	MASTER_SKILL_ADD_LIGHTNING_STORM_IMPROVED = 496,
	MASTER_SKILL_ADD_EARTH_PRISON_IMPROVED = 497,
	MASTER_SKILL_ADD_DL_WING_DEFENSE = 505,
	MASTER_SKILL_ADD_LEADERSHIP = 506,
	MASTER_SKILL_ADD_DL_WING_DAMAGE = 507,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED = 508,
	MASTER_SKILL_ADD_FORCE_WAVE_IMPROVED = 509,
	MASTER_SKILL_ADD_DARK_HORSE_DEFENSE = 510,
	MASTER_SKILL_ADD_GREATER_CRITICAL_DAMAGE_IMPROVED = 511,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED = 512,
	MASTER_SKILL_ADD_WEAPON_DAMAGE4 = 513,
	MASTER_SKILL_ADD_FIRE_BURST_ENHANCED = 514,
	MASTER_SKILL_ADD_GREATER_CRITICAL_DAMAGE_ENHANCED = 515,
	MASTER_SKILL_ADD_EARTHQUAKE_ENHANCED = 516,
	MASTER_SKILL_ADD_GREATER_CRITICAL_DAMAGE_MASTERED = 517,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED = 518,
	MASTER_SKILL_ADD_ELECTRIC_SPARK_IMPROVED = 519,
	MASTER_SKILL_ADD_FIRE_SCREAM_ENHANCED = 520,
	MASTER_SKILL_ADD_IRON_DEFENSE = 521,
	MASTER_SKILL_ADD_GREATER_CRITICAL_DAMAGE_EXTENDED = 522,
	MASTER_SKILL_ADD_BIRDS_IMPROVED = 523,
	MASTER_SKILL_ADD_IRON_DEFENSE_IMPROVED = 524,
	MASTER_SKILL_ADD_DARK_SPIRIT_DAMAGE = 526,
	MASTER_SKILL_ADD_SCEPTER_DAMAGE = 527,
	MASTER_SKILL_ADD_SHIELD_DEFENSE3 = 528,
	MASTER_SKILL_ADD_SCEPTER_PET_DAMAGE = 529,
	MASTER_SKILL_ADD_DARK_SPIRIT_CRITICAL_DAMAGE_RATE = 530,
	MASTER_SKILL_ADD_SCEPTER_MASTERY = 531,
	MASTER_SKILL_ADD_SHIELD_MASTERY3 = 532,
	MASTER_SKILL_ADD_DEFENSE_BY_LEADERSHIP = 533,
	MASTER_SKILL_ADD_DARK_SPIRIT_EXCELLENT_DAMAGE_RATE = 534,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE = 535,
	MASTER_SKILL_ADD_DARK_SPIRIT_ATTACK_SPEED = 536,
	MASTER_SKILL_ADD_DARK_SPIRIT_DOUBLE_DAMAGE_RATE = 538,
	MASTER_SKILL_ADD_DARK_SPIRIT_IGNORE_DEFENSE_RATE = 539,
	MASTER_SKILL_ADD_RF_WING_DEFENSE = 549,
	MASTER_SKILL_ADD_RF_WING_DAMAGE = 550,
	MASTER_SKILL_ADD_LARGE_RING_BLOWER_IMPROVED = 551,
	MASTER_SKILL_ADD_UPPER_BEAST_IMPROVED = 552,
	MASTER_SKILL_ADD_LARGE_RING_BLOWER_ENHANCED = 554,
	MASTER_SKILL_ADD_UPPER_BEAST_ENHANCED = 555,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE2 = 556,
	MASTER_SKILL_ADD_WEAPON_DAMAGE5 = 557,
	MASTER_SKILL_ADD_CHAIN_DRIVER_IMPROVED = 558,
	MASTER_SKILL_ADD_DARK_SIDE_IMPROVED = 559,
	MASTER_SKILL_ADD_DRAGON_LORE_IMPROVED = 560,
	MASTER_SKILL_ADD_DRAGON_LORE_ENHANCED = 561,
	MASTER_SKILL_ADD_CHAIN_DRIVER_ENHANCED = 562,
	MASTER_SKILL_ADD_DARK_SIDE_ENHANCED = 563,
	MASTER_SKILL_ADD_DRAGON_SLAYER_IMPROVED = 564,
	MASTER_SKILL_ADD_BLOOD_HOWLING = 565,
	MASTER_SKILL_ADD_DRAGON_SLAYER_ENHANCED = 566,
	MASTER_SKILL_ADD_BLOOD_HOWLING_IMPROVED = 567,
	MASTER_SKILL_ADD_GLOVE_DAMAGE = 568,
	MASTER_SKILL_ADD_GREATER_DEFENSE_SUCCESS_RATE_IMPROVED = 569,
	MASTER_SKILL_ADD_GLOVE_MASTERY = 571,
	MASTER_SKILL_ADD_GREATER_DEFENSE_SUCCESS_RATE_ENHANCED = 572,
	MASTER_SKILL_ADD_FITNESS_IMPROVED = 573,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE2 = 578,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP2 = 579,
	MASTER_SKILL_ADD_MAX_SD2 = 580,
	MASTER_SKILL_ADD_MP_RECOVERY_RATE2 = 581,
	MASTER_SKILL_ADD_POISON_RESISTANCE2 = 582,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE2 = 583,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE2 = 584,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE2 = 585,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE2 = 586,
	MASTER_SKILL_ADD_DEFENSE2 = 587,
	MASTER_SKILL_ADD_BP_RECOVERY_RATE2 = 588,
	MASTER_SKILL_ADD_ICE_RESISTANCE2 = 589,
	MASTER_SKILL_ADD_GUARDIAN_DURABILITY_RATE2 = 590,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE3 = 591,
	MASTER_SKILL_ADD_ARMOR_SET_BONUS2 = 593,
	MASTER_SKILL_ADD_REFLECT_DAMAGE2 = 594,
	MASTER_SKILL_ADD_ENERGY2 = 595,
	MASTER_SKILL_ADD_VITALITY2 = 596,
	MASTER_SKILL_ADD_DEXTERITY2 = 597,
	MASTER_SKILL_ADD_STRENGTH2 = 598,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE2 = 599,
	MASTER_SKILL_ADD_MAX_HP2 = 600,
	MASTER_SKILL_ADD_MAX_MP2 = 601,
	MASTER_SKILL_ADD_MAX_BP2 = 602,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP2 = 603,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE2 = 604,
	MASTER_SKILL_ADD_HUNT_SD2 = 605,
	MASTER_SKILL_ADD_HUNT_HP2 = 606,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE3 = 607,
	MASTER_SKILL_ADD_HUNT_MP2 = 608,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE3 = 609,
	MASTER_SKILL_ADD_CRITICAL_DAMAGE_RATE2 = 610,
	MASTER_SKILL_ADD_RESTORE_MP_RATE2 = 611,
	MASTER_SKILL_ADD_RESTORE_HP_RATE2 = 612,
	MASTER_SKILL_ADD_EXCELLENT_DAMAGE_RATE2 = 613,
	MASTER_SKILL_ADD_DOUBLE_DAMAGE_RATE2 = 614,
	MASTER_SKILL_ADD_IGNORE_DEFENSE_RATE2 = 615,
	MASTER_SKILL_ADD_RESTORE_SD_RATE2 = 616,
	MASTER_SKILL_ADD_TRIPLE_DAMAGE_RATE = 617,
	//Master Skill Tree Season X
	MASTER_SKILL_ADD_SHIELD_BLOCK = 623,
	MASTER_SKILL_ADD_WEAPON_FURY = 624,
	MASTER_SKILL_ADD_SHIELD_BLOCK_UPGRADE = 625,
	MASTER_SKILL_ADD_STEEL_ARMOR = 626,
	MASTER_SKILL_ADD_STRONG_MIND = 627,
	MASTER_SKILL_ADD_RECOVERY_HP = 628,
	MASTER_SKILL_ADD_RECOVERY_SHIELD = 629,
	#else
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_1 = 300,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_2 = 301,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_3 = 302,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_4 = 303,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_5 = 304,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP_1 = 305,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP_2 = 306,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP_3 = 307,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP_4 = 308,
	MASTER_SKILL_ADD_ATTACK_SUCCESS_RATE_PVP_5 = 309,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP_1 = 310,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP_2 = 311,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP_3 = 312,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP_4 = 313,
	MASTER_SKILL_ADD_DEFENSE_SUCCESS_RATE_PVP_5 = 314,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE_1 = 315,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE_2 = 316,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE_3 = 317,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE_4 = 318,
	MASTER_SKILL_ADD_ITEM_DURABILITY_RATE_5 = 319,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE_1 = 320,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE_2 = 321,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE_3 = 322,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE_4 = 323,
	MASTER_SKILL_ADD_JEWELRY_DURABILITY_RATE_5 = 324,
	MASTER_SKILL_ADD_POISON_RESISTANCE_1 = 325,
	MASTER_SKILL_ADD_POISON_RESISTANCE_2 = 326,
	MASTER_SKILL_ADD_POISON_RESISTANCE_3 = 327,
	MASTER_SKILL_ADD_POISON_RESISTANCE_4 = 328,
	MASTER_SKILL_ADD_POISON_RESISTANCE_5 = 329,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE_1 = 330,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE_2 = 331,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE_3 = 332,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE_4 = 333,
	MASTER_SKILL_ADD_LIGHTNING_RESISTANCE_5 = 334,
	MASTER_SKILL_ADD_ICE_RESISTANCE_1 = 335,
	MASTER_SKILL_ADD_ICE_RESISTANCE_2 = 336,
	MASTER_SKILL_ADD_ICE_RESISTANCE_3 = 337,
	MASTER_SKILL_ADD_ICE_RESISTANCE_4 = 338,
	MASTER_SKILL_ADD_ICE_RESISTANCE_5 = 339,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE_1 = 340,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE_2 = 341,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE_3 = 342,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE_4 = 343,
	MASTER_SKILL_ADD_HP_RECOVERY_RATE_5 = 344,
	MASTER_SKILL_ADD_MONEY_AMOUNT_DROP_RATE_1 = 345,
	MASTER_SKILL_ADD_MONEY_AMOUNT_DROP_RATE_2 = 346,
	MASTER_SKILL_ADD_MONEY_AMOUNT_DROP_RATE_3 = 347,
	MASTER_SKILL_ADD_MONEY_AMOUNT_DROP_RATE_4 = 348,
	MASTER_SKILL_ADD_MONEY_AMOUNT_DROP_RATE_5 = 349,
	MASTER_SKILL_ADD_DEFENSE_1 = 350,
	MASTER_SKILL_ADD_DEFENSE_2 = 351,
	MASTER_SKILL_ADD_DEFENSE_3 = 352,
	MASTER_SKILL_ADD_DEFENSE_4 = 353,
	MASTER_SKILL_ADD_DEFENSE_5 = 354,
	MASTER_SKILL_ADD_MAX_HP_1 = 355,
	MASTER_SKILL_ADD_MAX_HP_2 = 356,
	MASTER_SKILL_ADD_MAX_HP_3 = 357,
	MASTER_SKILL_ADD_MAX_HP_4 = 358,
	MASTER_SKILL_ADD_MAX_HP_5 = 359,
	MASTER_SKILL_ADD_MAX_BP_1 = 360,
	MASTER_SKILL_ADD_MAX_BP_2 = 361,
	MASTER_SKILL_ADD_MAX_BP_3 = 362,
	MASTER_SKILL_ADD_MAX_BP_4 = 363,
	MASTER_SKILL_ADD_MAX_BP_5 = 364,
	MASTER_SKILL_ADD_HUNT_MP_1 = 365,
	MASTER_SKILL_ADD_HUNT_MP_2 = 366,
	MASTER_SKILL_ADD_HUNT_MP_3 = 367,
	MASTER_SKILL_ADD_HUNT_MP_4 = 368,
	MASTER_SKILL_ADD_HUNT_MP_5 = 369,
	MASTER_SKILL_ADD_HUNT_HP_1 = 370,
	MASTER_SKILL_ADD_HUNT_HP_2 = 371,
	MASTER_SKILL_ADD_HUNT_HP_3 = 372,
	MASTER_SKILL_ADD_HUNT_HP_4 = 373,
	MASTER_SKILL_ADD_HUNT_HP_5 = 374,
	MASTER_SKILL_ADD_HUNT_SD_1 = 375,
	MASTER_SKILL_ADD_HUNT_SD_2 = 376,
	MASTER_SKILL_ADD_HUNT_SD_3 = 377,
	MASTER_SKILL_ADD_HUNT_SD_4 = 378,
	MASTER_SKILL_ADD_HUNT_SD_5 = 379,
	MASTER_SKILL_ADD_EXPERIENCE_RATE_1 = 380,
	MASTER_SKILL_ADD_EXPERIENCE_RATE_2 = 381,
	MASTER_SKILL_ADD_EXPERIENCE_RATE_3 = 382,
	MASTER_SKILL_ADD_EXPERIENCE_RATE_4 = 383,
	MASTER_SKILL_ADD_EXPERIENCE_RATE_5 = 384,
	MASTER_SKILL_ADD_MAX_SD_1 = 385,
	MASTER_SKILL_ADD_MAX_SD_2 = 386,
	MASTER_SKILL_ADD_MAX_SD_3 = 387,
	MASTER_SKILL_ADD_MAX_SD_4 = 388,
	MASTER_SKILL_ADD_MAX_SD_5 = 389,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE_1 = 390,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE_2 = 391,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE_3 = 392,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE_4 = 393,
	MASTER_SKILL_ADD_SD_RECOVERY_RATE_5 = 394,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE_1 = 395,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE_2 = 396,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE_3 = 397,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE_4 = 398,
	MASTER_SKILL_ADD_MAX_PHYSI_DAMAGE_5 = 399,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE_1 = 400,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE_2 = 401,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE_3 = 402,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE_4 = 403,
	MASTER_SKILL_ADD_MIN_PHYSI_DAMAGE_5 = 404,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE_1 = 405,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE_2 = 406,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE_3 = 407,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE_4 = 408,
	MASTER_SKILL_ADD_MP_CONSUMPTION_RATE_5 = 409,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE_1 = 410,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE_2 = 411,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE_3 = 412,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE_4 = 413,
	MASTER_SKILL_ADD_MAX_MAGIC_DAMAGE_5 = 414,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE_1 = 415,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE_2 = 416,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE_3 = 417,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE_4 = 418,
	MASTER_SKILL_ADD_MIN_MAGIC_DAMAGE_5 = 419,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE_1 = 420,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE_2 = 421,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE_3 = 422,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE_4 = 423,
	MASTER_SKILL_ADD_PET_DURABILITY_RATE_5 = 424,
	MASTER_SKILL_ADD_MAX_DAMAGE_1 = 425,
	MASTER_SKILL_ADD_MAX_DAMAGE_2 = 426,
	MASTER_SKILL_ADD_MAX_DAMAGE_3 = 427,
	MASTER_SKILL_ADD_MAX_DAMAGE_4 = 428,
	MASTER_SKILL_ADD_MAX_DAMAGE_5 = 429,
	MASTER_SKILL_ADD_MIN_DAMAGE_1 = 430,
	MASTER_SKILL_ADD_MIN_DAMAGE_2 = 431,
	MASTER_SKILL_ADD_MIN_DAMAGE_3 = 432,
	MASTER_SKILL_ADD_MIN_DAMAGE_4 = 433,
	MASTER_SKILL_ADD_MIN_DAMAGE_5 = 434,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED_1 = 435,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED_2 = 436,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED_3 = 437,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED_4 = 438,
	MASTER_SKILL_ADD_MANA_SHIELD_IMPROVED_5 = 439,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED_1 = 440,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED_2 = 441,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED_3 = 442,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED_4 = 443,
	MASTER_SKILL_ADD_HELL_FIRE_IMPROVED_5 = 444,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1_1 = 445,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1_2 = 446,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1_3 = 447,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1_4 = 448,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED1_5 = 449,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED_1 = 450,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED_2 = 451,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED_3 = 452,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED_4 = 453,
	MASTER_SKILL_ADD_ICE_STORM_IMPROVED_5 = 454,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1_1 = 455,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1_2 = 456,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1_3 = 457,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1_4 = 458,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED1_5 = 459,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED_1 = 460,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED_2 = 461,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED_3 = 462,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED_4 = 463,
	MASTER_SKILL_ADD_DEATH_STAB_IMPROVED_5 = 464,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED_1 = 465,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED_2 = 466,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED_3 = 467,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED_4 = 468,
	MASTER_SKILL_ADD_RAGEFUL_BLOW_IMPROVED_5 = 469,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED_1 = 470,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED_2 = 471,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED_3 = 472,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED_4 = 473,
	MASTER_SKILL_ADD_GREATER_LIFE_IMPROVED_5 = 474,
	MASTER_SKILL_ADD_HEAL_IMPROVED_1 = 475,
	MASTER_SKILL_ADD_HEAL_IMPROVED_2 = 476,
	MASTER_SKILL_ADD_HEAL_IMPROVED_3 = 477,
	MASTER_SKILL_ADD_HEAL_IMPROVED_4 = 478,
	MASTER_SKILL_ADD_HEAL_IMPROVED_5 = 479,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED_1 = 480,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED_2 = 481,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED_3 = 482,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED_4 = 483,
	MASTER_SKILL_ADD_GREATER_DEFENSE_IMPROVED_5 = 484,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED_1 = 485,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED_2 = 486,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED_3 = 487,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED_4 = 488,
	MASTER_SKILL_ADD_GREATER_DAMAGE_IMPROVED_5 = 489,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED_1 = 490,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED_2 = 491,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED_3 = 492,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED_4 = 493,
	MASTER_SKILL_ADD_TRIPLE_SHOT_IMPROVED_5 = 494,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2_1 = 495,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2_2 = 496,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2_3 = 497,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2_4 = 498,
	MASTER_SKILL_ADD_TWISTING_SLASH_IMPROVED2_5 = 499,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED_1 = 500,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED_2 = 501,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED_3 = 502,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED_4 = 503,
	MASTER_SKILL_ADD_FIRE_SLASH_IMPROVED_5 = 504,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED_1 = 505,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED_2 = 506,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED_3 = 507,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED_4 = 508,
	MASTER_SKILL_ADD_POWER_SLASH_IMPROVED_5 = 509,
	MASTER_SKILL_ADD_BLAST_IMPROVED_1 = 510,
	MASTER_SKILL_ADD_BLAST_IMPROVED_2 = 511,
	MASTER_SKILL_ADD_BLAST_IMPROVED_3 = 512,
	MASTER_SKILL_ADD_BLAST_IMPROVED_4 = 513,
	MASTER_SKILL_ADD_BLAST_IMPROVED_5 = 514,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED_1 = 515,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED_2 = 516,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED_3 = 517,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED_4 = 518,
	MASTER_SKILL_ADD_EARTHQUAKE_IMPROVED_5 = 519,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED_1 = 520,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED_2 = 521,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED_3 = 522,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED_4 = 523,
	MASTER_SKILL_ADD_FIRE_BURST_IMPROVED_5 = 524,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED_1 = 525,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED_2 = 526,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED_3 = 527,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED_4 = 528,
	MASTER_SKILL_ADD_FIRE_SCREAM_IMPROVED_5 = 529,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2_1 = 530,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2_2 = 531,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2_3 = 532,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2_4 = 533,
	MASTER_SKILL_ADD_EVIL_SPIRIT_IMPROVED2_5 = 534,
	MASTER_SKILL_ADD_SLEEP_IMPROVED_1 = 535,
	MASTER_SKILL_ADD_SLEEP_IMPROVED_2 = 536,
	MASTER_SKILL_ADD_SLEEP_IMPROVED_3 = 537,
	MASTER_SKILL_ADD_SLEEP_IMPROVED_4 = 538,
	MASTER_SKILL_ADD_SLEEP_IMPROVED_5 = 539,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED_1 = 540,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED_2 = 541,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED_3 = 542,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED_4 = 543,
	MASTER_SKILL_ADD_CHAIN_LIGHTNING_IMPROVED_5 = 544,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED_1 = 545,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED_2 = 546,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED_3 = 547,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED_4 = 548,
	MASTER_SKILL_ADD_RED_STORM_IMPROVED_5 = 549,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED_1 = 550,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED_2 = 551,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED_3 = 552,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED_4 = 553,
	MASTER_SKILL_ADD_DRAIN_LIFE_IMPROVED_5 = 554,
	#endif
};

//**********************************************//
//************ Client -> GameServer ************//
//**********************************************//

struct PMSG_MASTER_SKILL_RECV
{
	PSBMSG_HEAD header; // C1:F3:52
	WORD MasterSkill;
	WORD MasterEmpty;
};

//**********************************************//
//************ GameServer -> Client ************//
//**********************************************//

struct PMSG_MASTER_INFO_SEND
{
	PSBMSG_HEAD header; // C1:F3:50
	uint16_t MasterLevel;
	uint16_t MasterPoint;

	uint32_t LifeMax;
	uint32_t ManaMax;
	uint32_t ShieldMax;
	uint32_t SkillManaMax;

	uint8_t Experience[8];
	uint8_t BaseExperience[8];
	uint8_t NextExperience[8];
};

struct PMSG_MASTER_LEVEL_UP_SEND
{
	PSBMSG_HEAD header; // C1:F3:51
	uint16_t MasterLevel;
	uint16_t MasterPoint;
	uint16_t MinMasterLevel;
	uint16_t MaxMasterLevel;

	uint32_t LifeMax;
	uint32_t ManaMax;
	uint32_t ShieldMax;
	uint32_t SkillManaMax;

	uint8_t Experience[8];
	uint8_t BaseExperience[8];
	uint8_t NextExperience[8];
};

struct PMSG_MASTER_SKILL_SEND
{
	PSBMSG_HEAD header; // C1:F3:52
	BYTE type;
	BYTE flag;
	#if(GAMESERVER_UPDATE>=602)
	WORD MasterPoint;
	#else
	WORD MasterPoint;
	WORD MasterSkill;
	WORD MasterEmpty;
	#endif
};

struct PMSG_MASTER_SKILL_LIST_SEND
{
	PSWMSG_HEAD header; // C1:F3:53
	DWORD count;
};

struct PMSG_MASTER_SKILL_LIST
{
	BYTE skill;
	BYTE level;
	float MainValue;
	float NextValue;
};

//**********************************************//
//********** DataServer -> GameServer **********//
//**********************************************//

struct SDHP_MASTER_SKILL_TREE_RECV
{
	PSWMSG_HEAD header; // C2:0D:00
	WORD index;
	char account[11];
	char name[11];
	DWORD MasterLevel;
	DWORD MasterPoint;
	QWORD MasterExperience;
	#if(GAMESERVER_UPDATE>=602)
	BYTE MasterSkill[MAX_MASTER_SKILL_LIST][3];
	#endif
};

//**********************************************//
//********** GameServer -> DataServer **********//
//**********************************************//

struct SDHP_MASTER_SKILL_TREE_SEND
{
	PSBMSG_HEAD header; // C1:0D:00
	WORD index;
	char account[11];
	char name[11];
};

struct SDHP_MASTER_SKILL_TREE_SAVE_SEND
{
	PSWMSG_HEAD header; // C2:0D:30
	WORD index;
	char account[11];
	char name[11];
	DWORD MasterLevel;
	DWORD MasterPoint;
	QWORD MasterExperience;
	#if(GAMESERVER_UPDATE>=602)
	BYTE MasterSkill[MAX_MASTER_SKILL_LIST][3];
	#endif
};

//**********************************************//
//**********************************************//
//**********************************************//

struct MASTER_SKILL_TREE_INFO
{
	#if(GAMESERVER_UPDATE>=602)
	int Index;
	int Group;
	int Rank;
	int MinLevel;
	int MaxLevel;
	flt MainValue[MAX_SKILL_TREE_LEVEL];
	int RelatedSkill;
	int ReplaceSkill;
	int RequireSkill[2];
	int RequireClass[MAX_CLASS];
	#else
	int Index;
	int Group;
	int Rank;
	int MinLevel;
	int MaxLevel;
	int MainValue;
	int RelatedSkill;
	int ReplaceSkill;
	int RequireSkill[2];
	#endif
};

class CMasterSkillTree
{
public:
	CMasterSkillTree();
	virtual ~CMasterSkillTree();
	void Load(char* path);
	bool GetInfo(int index,MASTER_SKILL_TREE_INFO* lpInfo);
	int GetMasterSkillRelated(int index);
	flt GetMasterSkillValue(int index,int level);
	int GetMasterSkillValue(LPOBJ lpObj,int index);
	int GetMasterSkillLevel(LPOBJ lpObj,int index);
	int GetMasterSkillWeapon(LPOBJ lpObj,int index);
	int GetMasterSkillDamageMin(LPOBJ lpObj,int index);
	int GetMasterSkillDamageMax(LPOBJ lpObj,int index);
	bool CheckMasterLevel(LPOBJ lpObj);
	bool CheckMasterReplaceSkill(LPOBJ lpObj,int index);
	bool CheckMasterRequireSkill(LPOBJ lpObj,int index);
	bool CheckMasterRequireGroup(LPOBJ lpObj,int group,int rank);
	void SetMasterLevelExperienceTable();
	void CalcMasterLevelNextExperience(LPOBJ lpObj);
	void CalcMasterSkillTreeOption(LPOBJ lpObj,bool flag);
	void InsertOption(LPOBJ lpObj,int index,int value,bool flag);
	bool ReplaceMasterSkill(LPOBJ lpObj,int index,int skill,int level);
	void CGMasterSkillRecv(PMSG_MASTER_SKILL_RECV* lpMsg,int aIndex);
	void GCMasterInfoSend(LPOBJ lpObj);
	void GCMasterLevelUpSend(LPOBJ lpObj);
	void GCMasterSkillSend(int aIndex,BYTE type,BYTE flag,int MasterPoint,int MasterSkill,int MasterEmpty);
	void GCMasterSkillListSend(int aIndex);
	void DGMasterSkillTreeRecv(SDHP_MASTER_SKILL_TREE_RECV* lpMsg);
	void GDMasterSkillTreeSend(int aIndex);
	void GDMasterSkillTreeSaveSend(int aIndex);
private:
	std::map<int,MASTER_SKILL_TREE_INFO> m_MasterSkillTreeInfo;
	QWORD m_MasterLevelExperienceTable[MAX_CHARACTER_MASTER_LEVEL+1];
};

extern CMasterSkillTree gMasterSkillTree;
